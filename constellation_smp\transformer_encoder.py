"""
基于Transformer的星座任务规划编码器
提升大规模任务处理性能
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class PositionalEncoding(nn.Module):
    """位置编码模块"""
    
    def __init__(self, d_model, max_len=5000, dropout=0.1):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        """
        x: (seq_len, batch_size, d_model)
        """
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class MultiHeadSelfAttention(nn.Module):
    """多头自注意力机制"""
    
    def __init__(self, d_model, n_heads, dropout=0.1):
        super(MultiHeadSelfAttention, self).__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 初始化权重
        nn.init.xavier_uniform_(self.w_q.weight)
        nn.init.xavier_uniform_(self.w_k.weight)
        nn.init.xavier_uniform_(self.w_v.weight)
        nn.init.xavier_uniform_(self.w_o.weight)
    
    def forward(self, x, mask=None):
        """
        x: (batch_size, seq_len, d_model)
        mask: (batch_size, seq_len, seq_len) or None
        """
        batch_size, seq_len, d_model = x.size()
        residual = x
        
        # 线性变换
        Q = self.w_q(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        attention_output = self.scaled_dot_product_attention(Q, K, V, mask)
        
        # 拼接多头
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, d_model)
        
        # 输出投影
        output = self.w_o(attention_output)
        output = self.dropout(output)
        
        # 残差连接和层归一化
        output = self.layer_norm(output + residual)
        
        return output
    
    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        """
        Q, K, V: (batch_size, n_heads, seq_len, d_k)
        mask: (batch_size, 1, seq_len, seq_len) or None
        """
        d_k = Q.size(-1)
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        output = torch.matmul(attention_weights, V)
        return output


class FeedForward(nn.Module):
    """前馈神经网络"""
    
    def __init__(self, d_model, d_ff, dropout=0.1):
        super(FeedForward, self).__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 初始化权重
        nn.init.xavier_uniform_(self.linear1.weight)
        nn.init.xavier_uniform_(self.linear2.weight)
    
    def forward(self, x):
        """
        x: (batch_size, seq_len, d_model)
        """
        residual = x
        x = self.linear2(self.dropout(F.gelu(self.linear1(x))))
        x = self.dropout(x)
        return self.layer_norm(x + residual)


class TransformerEncoderLayer(nn.Module):
    """Transformer编码器层"""
    
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(TransformerEncoderLayer, self).__init__()
        self.self_attention = MultiHeadSelfAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
    
    def forward(self, x, mask=None):
        """
        x: (batch_size, seq_len, d_model)
        mask: (batch_size, seq_len, seq_len) or None
        """
        x = self.self_attention(x, mask)
        x = self.feed_forward(x)
        return x


class ConstellationTransformerEncoder(nn.Module):
    """星座任务规划的Transformer编码器"""
    
    def __init__(self, input_size, d_model=256, n_heads=8, n_layers=6, d_ff=1024, 
                 max_len=1000, dropout=0.1, num_satellites=3, constellation_mode='cooperative'):
        super(ConstellationTransformerEncoder, self).__init__()
        self.d_model = d_model
        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode
        
        # 输入投影层
        self.input_projection = nn.Linear(input_size, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model, max_len, dropout)
        
        # Transformer编码器层
        self.encoder_layers = nn.ModuleList([
            TransformerEncoderLayer(d_model, n_heads, d_ff, dropout)
            for _ in range(n_layers)
        ])
        
        # 卫星特定的编码器
        self.satellite_encoders = nn.ModuleList([
            nn.Linear(d_model, d_model) for _ in range(num_satellites)
        ])
        
        # 星座级别的融合层
        self.constellation_fusion = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=n_heads,
            dropout=dropout
        )
        
        # 输出投影层
        self.output_projection = nn.Linear(d_model, d_model)
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
        # dropout
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        nn.init.xavier_uniform_(self.input_projection.weight)
        nn.init.xavier_uniform_(self.output_projection.weight)
        for encoder in self.satellite_encoders:
            nn.init.xavier_uniform_(encoder.weight)
    
    def forward(self, static, dynamic):
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)
        
        返回:
        constellation_features: (batch_size, d_model, seq_len) - 星座级别特征
        satellite_features: (batch_size, d_model, seq_len, num_satellites) - 每颗卫星的特征
        """
        batch_size, static_size, seq_len = static.size()
        dynamic_size = dynamic.size(1)
        
        # 处理每颗卫星的特征
        satellite_features_list = []
        
        for sat_idx in range(self.num_satellites):
            # 拼接静态和动态特征
            sat_dynamic = dynamic[:, :, :, sat_idx]  # (batch_size, dynamic_size, seq_len)
            combined_features = torch.cat([static, sat_dynamic], dim=1)  # (batch_size, input_size, seq_len)
            
            # 转换为 (batch_size, seq_len, input_size)
            combined_features = combined_features.transpose(1, 2)
            
            # 输入投影
            x = self.input_projection(combined_features)  # (batch_size, seq_len, d_model)
            
            # 位置编码
            x = x.transpose(0, 1)  # (seq_len, batch_size, d_model)
            x = self.pos_encoding(x)
            x = x.transpose(0, 1)  # (batch_size, seq_len, d_model)
            
            # 通过Transformer编码器层
            for encoder_layer in self.encoder_layers:
                x = encoder_layer(x)
            
            # 卫星特定编码
            x = self.satellite_encoders[sat_idx](x)
            x = self.dropout(x)
            
            # 转换回 (batch_size, d_model, seq_len)
            x = x.transpose(1, 2)
            satellite_features_list.append(x)
        
        # 堆叠所有卫星特征
        satellite_features = torch.stack(satellite_features_list, dim=3)  # (batch_size, d_model, seq_len, num_satellites)
        
        # 星座级别的特征融合
        if self.constellation_mode in ['cooperative', 'hybrid']:
            # 将所有卫星特征进行注意力融合
            # 重塑为 (seq_len * num_satellites, batch_size, d_model)
            sat_features_flat = satellite_features.permute(2, 3, 0, 1).contiguous().view(
                seq_len * self.num_satellites, batch_size, self.d_model)
            
            # 自注意力融合
            fused_features, _ = self.constellation_fusion(
                sat_features_flat, sat_features_flat, sat_features_flat)
            
            # 重塑回 (batch_size, d_model, seq_len, num_satellites)
            fused_features = fused_features.view(
                seq_len, self.num_satellites, batch_size, self.d_model).permute(2, 3, 0, 1)
            
            # 更新卫星特征
            satellite_features = fused_features
        
        # 计算星座级别的特征（所有卫星特征的平均）
        constellation_features = torch.mean(satellite_features, dim=3)  # (batch_size, d_model, seq_len)
        
        # 输出投影和归一化
        constellation_features = constellation_features.transpose(1, 2)  # (batch_size, seq_len, d_model)
        constellation_features = self.output_projection(constellation_features)
        constellation_features = self.layer_norm(constellation_features)
        constellation_features = constellation_features.transpose(1, 2)  # (batch_size, d_model, seq_len)
        
        return constellation_features, satellite_features


class ConstellationTransformerCritic(nn.Module):
    """基于Transformer的星座状态评论家"""
    
    def __init__(self, static_size, dynamic_size, d_model=256, n_heads=8, n_layers=3, 
                 num_satellites=3, constellation_mode='cooperative'):
        super(ConstellationTransformerCritic, self).__init__()
        
        # 使用相同的Transformer编码器
        self.transformer_encoder = ConstellationTransformerEncoder(
            static_size + dynamic_size, d_model, n_heads, n_layers, 
            d_model * 4, 1000, 0.1, num_satellites, constellation_mode
        )
        
        # 评论家网络
        self.critic_head = nn.Sequential(
            nn.Conv1d(d_model, 128, kernel_size=1),
            nn.ReLU(),
            nn.Conv1d(128, 64, kernel_size=1),
            nn.ReLU(),
            nn.Conv1d(64, 1, kernel_size=1)
        )
        
        # 初始化权重
        for m in self.critic_head:
            if isinstance(m, nn.Conv1d):
                nn.init.xavier_uniform_(m.weight)
    
    def forward(self, static, dynamic):
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)
        
        返回:
        value: (batch_size,) - 状态价值估计
        """
        # 获取Transformer编码的特征
        constellation_features, _ = self.transformer_encoder(static, dynamic)
        
        # 通过评论家网络
        value = self.critic_head(constellation_features)  # (batch_size, 1, seq_len)
        
        # 全局平均池化
        value = torch.mean(value, dim=2).squeeze(1)  # (batch_size,)
        
        return value
