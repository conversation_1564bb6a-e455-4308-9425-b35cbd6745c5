#!/usr/bin/env python3
"""
测试revenue_rate修复的脚本
"""

import torch
import sys
import os

# 添加路径
sys.path.append('constellation_smp')

from constellation_smp import ConstellationSMPDataset, reward

def test_revenue_rate_fix():
    """测试revenue_rate计算修复"""
    print("=== 测试Revenue Rate计算修复 ===")
    
    # 创建测试数据集
    dataset = ConstellationSMPDataset(size=10, num_satellites=3)
    
    # 获取一个样本
    static, dynamic, _ = dataset[0]
    static_batch = static.unsqueeze(0)
    dynamic_batch = dynamic.unsqueeze(0)
    
    print(f"Static shape: {static_batch.shape}")
    print(f"Dynamic shape: {dynamic_batch.shape}")
    
    # 模拟任务执行（选择前5个任务）
    tour_indices = torch.tensor([[1, 2, 3, 4, 5]], dtype=torch.long)
    satellite_indices = torch.tensor([[0, 1, 2, 0, 1]], dtype=torch.long)
    
    print(f"Tour indices: {tour_indices}")
    print(f"Satellite indices: {satellite_indices}")
    
    # 计算奖励和收益率
    R, revenue_rate, distance, memory, power = reward(
        static_batch, tour_indices, satellite_indices, 'competitive'
    )
    
    print(f"\n=== 结果 ===")
    print(f"奖励值: {R.item():.4f}")
    print(f"收益率: {revenue_rate.item():.4f}")
    print(f"距离: {distance.item():.4f}")
    print(f"内存: {memory.item():.4f}")
    print(f"能量: {power.item():.4f}")
    
    # 验证收益率是否在合理范围内
    if revenue_rate.item() > 1.0:
        print(f"⚠️  警告: 收益率 {revenue_rate.item():.4f} 仍然超过1.0")
    else:
        print(f"✅ 收益率 {revenue_rate.item():.4f} 在合理范围内")
    
    # 计算理论最大收益
    all_possible_revenue = torch.sum(static_batch[:, 4, :], dim=1)
    print(f"\n=== 详细分析 ===")
    print(f"理论最大收益: {all_possible_revenue.item():.4f}")
    
    # 计算实际完成任务的收益
    completed_revenue = 0.0
    for task_idx in tour_indices[0]:
        if task_idx > 0:  # 排除填充的0
            task_revenue = static_batch[0, 4, task_idx].item()
            completed_revenue += task_revenue
            print(f"任务 {task_idx}: 收益 {task_revenue:.4f}")
    
    print(f"实际完成任务总收益: {completed_revenue:.4f}")
    print(f"理论收益率: {completed_revenue / all_possible_revenue.item():.4f}")
    
    return revenue_rate.item() <= 1.0

def test_multiple_satellites_same_task():
    """测试多颗卫星执行相同任务的情况"""
    print("\n=== 测试多卫星执行相同任务 ===")
    
    dataset = ConstellationSMPDataset(size=10, num_satellites=3)
    static, dynamic, _ = dataset[0]
    static_batch = static.unsqueeze(0)
    dynamic_batch = dynamic.unsqueeze(0)
    
    # 模拟多颗卫星执行相同任务
    tour_indices = torch.tensor([[1, 1, 2, 2, 3]], dtype=torch.long)  # 任务1和2被重复执行
    satellite_indices = torch.tensor([[0, 1, 0, 2, 1]], dtype=torch.long)
    
    print(f"Tour indices (重复任务): {tour_indices}")
    print(f"Satellite indices: {satellite_indices}")
    
    R, revenue_rate, distance, memory, power = reward(
        static_batch, tour_indices, satellite_indices, 'competitive'
    )
    
    print(f"收益率: {revenue_rate.item():.4f}")
    
    if revenue_rate.item() > 1.0:
        print(f"⚠️  警告: 即使修复后，收益率 {revenue_rate.item():.4f} 仍然超过1.0")
        return False
    else:
        print(f"✅ 修复成功: 收益率 {revenue_rate.item():.4f} 在合理范围内")
        return True

if __name__ == "__main__":
    try:
        # 测试基本情况
        test1_passed = test_revenue_rate_fix()
        
        # 测试多卫星相同任务情况
        test2_passed = test_multiple_satellites_same_task()
        
        print(f"\n=== 测试总结 ===")
        print(f"基本测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
        print(f"重复任务测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
        
        if test1_passed and test2_passed:
            print("🎉 所有测试通过！Revenue rate修复成功。")
        else:
            print("⚠️  部分测试失败，需要进一步调试。")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
