🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp200\multi_mode_training_2025_08_05_10_04_35
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 200
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 16
seed: 12346
train-size: 1000
valid-size: 100
epochs: 2
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_05_10_04_43
使用模型: gpn_transformer
Actor参数数量: 5,929,732
Critic参数数量: 2,942,081

开始训练 Epoch 1/2
Batch 0: Reward: 22.0687, Loss: 39523.1641, Revenue: 0.1743, LoadBalance: 0.6892, Tasks: [S0:221(31.4%), S1:224(31.8%), S2:259(36.8%)], ActorGrad: 5462.0347, CriticGrad: 4317.7456, Advantage: μ=1.088, σ=10.000, range=[-7.87, 18.77]
Batch 5: Reward: -1325.6624, Loss: 1756392.2500, Revenue: 0.5466, LoadBalance: 0.0000, Tasks: [S0:86(3.7%), S1:53(2.3%), S2:2165(94.0%)], ActorGrad: 841698.3125, CriticGrad: 33862.9531, Advantage: μ=-1323.288, σ=75.191, range=[-1451.53, -1147.72]
Epoch 1, Batch 10/63, loss: 880269.271↑, reward: -756.372↓, critic_reward: -1.624, revenue_rate: 0.4389, distance: 16.8537, memory: 0.3507, power: 0.5767, lr: 0.000400, took: 63.411s
Batch 10: Reward: -644.6966, Loss: 501448.2500, Revenue: 0.5597, LoadBalance: 0.0000, Tasks: [S0:1916(74.8%), S1:68(2.7%), S2:576(22.5%)], ActorGrad: 13293.5527, CriticGrad: 10379.8574, Advantage: μ=-20.560, σ=10.000, range=[-57.07, -13.42]
Batch 15: Reward: -2038.7378, Loss: 4431694.0000, Revenue: 0.6992, LoadBalance: 0.0000, Tasks: [S0:2961(97.9%), S1:47(1.6%), S2:16(0.5%)], ActorGrad: 29806.2012, CriticGrad: 28658.2207, Advantage: μ=-36.326, σ=10.000, range=[-46.84, -23.98]
Epoch 1, Batch 20/63, loss: 2998778.875↑, reward: -1623.927↓, critic_reward: -4.600, revenue_rate: 0.6484, distance: 25.5659, memory: 0.5806, power: 0.8848, lr: 0.000400, took: 101.083s
Batch 20: Reward: -2141.0229, Loss: 4968902.0000, Revenue: 0.7415, LoadBalance: 0.0000, Tasks: [S0:3141(98.6%), S1:18(0.6%), S2:25(0.8%)], ActorGrad: 25929.3691, CriticGrad: 29966.6758, Advantage: μ=-32.432, σ=10.000, range=[-55.35, -22.56]
Batch 25: Reward: -1418.7351, Loss: 2001955.7500, Revenue: 0.5745, LoadBalance: 0.0000, Tasks: [S0:2498(95.8%), S1:68(2.6%), S2:42(1.6%)], ActorGrad: 908793.6250, CriticGrad: 20510.8047, Advantage: μ=-1413.146, σ=72.833, range=[-1503.44, -1260.47]
Epoch 1, Batch 30/63, loss: 3061076.038↓, reward: -1676.888↑, critic_reward: -5.344, revenue_rate: 0.6274, distance: 24.7529, memory: 0.5002, power: 0.8798, lr: 0.000400, took: 99.789s
Batch 30: Reward: -1372.1544, Loss: 1949306.0000, Revenue: 0.5158, LoadBalance: 0.0000, Tasks: [S0:2321(93.6%), S1:35(1.4%), S2:124(5.0%)], ActorGrad: 27181.0586, CriticGrad: 18971.5879, Advantage: μ=-46.395, σ=10.000, range=[-82.59, -38.93]
Batch 35: Reward: -462.5875, Loss: 287580.7188, Revenue: 0.5097, LoadBalance: 0.0420, Tasks: [S0:674(29.7%), S1:59(2.6%), S2:1539(67.7%)], ActorGrad: 13493.4854, CriticGrad: 6544.8125, Advantage: μ=-15.754, σ=10.000, range=[-51.32, -9.96]
Epoch 1, Batch 40/63, loss: 792625.576↓, reward: -800.888↑, critic_reward: -5.689, revenue_rate: 0.4998, distance: 19.8815, memory: 0.4661, power: 0.7147, lr: 0.000400, took: 72.956s
Batch 40: Reward: -1109.5295, Loss: 1336575.0000, Revenue: 0.5626, LoadBalance: 0.0000, Tasks: [S0:270(10.6%), S1:78(3.1%), S2:2196(86.3%)], ActorGrad: 19673.5820, CriticGrad: 15936.5596, Advantage: μ=-31.007, σ=10.000, range=[-56.71, -21.79]
Batch 45: Reward: -399.9629, Loss: 169102.0938, Revenue: 0.3015, LoadBalance: 0.0844, Tasks: [S0:111(8.8%), S1:283(22.4%), S2:870(68.8%)], ActorGrad: 12121.7783, CriticGrad: 5492.8325, Advantage: μ=-32.399, σ=10.000, range=[-47.48, -19.89]
Epoch 1, Batch 50/63, loss: 650098.376↓, reward: -675.566↑, critic_reward: -5.952, revenue_rate: 0.3908, distance: 14.8233, memory: 0.3073, power: 0.5375, lr: 0.000400, took: 55.887s
Batch 50: Reward: -505.8693, Loss: 255578.7812, Revenue: 0.3872, LoadBalance: 0.0123, Tasks: [S0:115(7.0%), S1:1202(72.9%), S2:331(20.1%)], ActorGrad: 183437.7500, CriticGrad: 6891.9048, Advantage: μ=-499.719, σ=79.058, range=[-676.10, -347.36]
Batch 55: Reward: -363.7005, Loss: 137454.8125, Revenue: 0.3634, LoadBalance: 0.0954, Tasks: [S0:117(7.5%), S1:1039(66.9%), S2:396(25.5%)], ActorGrad: 14900.1523, CriticGrad: 5095.9717, Advantage: μ=-34.923, σ=10.000, range=[-52.18, -22.32]
Epoch 1, Batch 60/63, loss: 304109.433↑, reward: -522.332↓, critic_reward: -6.286, revenue_rate: 0.3412, distance: 12.7120, memory: 0.2920, power: 0.4664, lr: 0.000400, took: 50.205s
Batch 60: Reward: -645.8480, Loss: 423338.1875, Revenue: 0.4036, LoadBalance: 0.0048, Tasks: [S0:125(6.9%), S1:284(15.7%), S2:1399(77.4%)], ActorGrad: 22069.9863, CriticGrad: 9420.4102, Advantage: μ=-50.902, σ=10.000, range=[-66.59, -29.88]

📊 Epoch 1 训练统计:
  平均奖励: -980.1614
  平均损失: 1388697.8721
  平均收益率: 0.4831
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: -260.196, revenue_rate: 0.1987, efficiency: 0.0417, distance: 6.5632, memory: 0.0972, power: 0.2083
Test Batch 1/7, reward: -213.269, revenue_rate: 0.2756, efficiency: 0.0867, distance: 9.5546, memory: 0.1640, power: 0.3019
Test Batch 2/7, reward: -189.783, revenue_rate: 0.2339, efficiency: 0.0585, distance: 7.5359, memory: 0.0928, power: 0.2399
Test Batch 3/7, reward: -193.333, revenue_rate: 0.2171, efficiency: 0.0519, distance: 7.4343, memory: 0.1791, power: 0.2332
Test Batch 4/7, reward: -171.878, revenue_rate: 0.2521, efficiency: 0.0717, distance: 9.0548, memory: 0.1752, power: 0.2864
Test Batch 5/7, reward: -190.045, revenue_rate: 0.2115, efficiency: 0.0505, distance: 7.7603, memory: 0.1600, power: 0.2499
Test Batch 6/7, reward: -154.877, revenue_rate: 0.1974, efficiency: 0.0434, distance: 6.7667, memory: 0.1301, power: 0.2125
Test Summary - Avg reward: -201.156±101.954, revenue_rate: 0.2301±0.0352, efficiency: 0.0595, completion_rate: 0.2547, distance: 7.9351, memory: 0.1441, power: 0.2516
Load Balance - Avg balance score: 0.3482±0.1853
Task Distribution by Satellite:
  Satellite 1: 928 tasks (18.55%)
  Satellite 2: 2880 tasks (57.55%)
  Satellite 3: 1196 tasks (23.90%)
✅ 验证完成 - Epoch 1, reward: -201.156, revenue_rate: 0.2301, distance: 7.9351, memory: 0.1441, power: 0.2516
  ⚠️ 欠拟合: 训练验证差距 = -779.0055
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_05_10_04_43 (验证集奖励: -201.1559)

开始训练 Epoch 2/2
Batch 0: Reward: -150.3538, Loss: 24991.3086, Revenue: 0.1918, LoadBalance: 0.4310, Tasks: [S0:161(18.6%), S1:467(54.1%), S2:236(27.3%)], ActorGrad: 50824.4336, CriticGrad: 2015.2677, Advantage: μ=-143.914, σ=67.567, range=[-358.85, -57.39]
Batch 5: Reward: -811.3602, Loss: 717674.3125, Revenue: 0.5299, LoadBalance: 0.0000, Tasks: [S0:1989(80.2%), S1:450(18.1%), S2:41(1.7%)], ActorGrad: 21812.5488, CriticGrad: 11556.8125, Advantage: μ=-29.376, σ=10.000, range=[-64.88, -20.87]
