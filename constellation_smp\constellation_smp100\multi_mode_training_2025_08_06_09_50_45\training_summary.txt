🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_06_09_50_45
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_06_09_50_49
使用模型: gpn_transformer
Actor参数数量: 4,385,924
Critic参数数量: 2,153,729

开始训练 Epoch 1/3
Batch 0: Reward: -61.6514, Loss: 13696.6455, Revenue: 0.3593, LoadBalance: 0.5635, Tasks: [S0:694(48.2%), S1:376(26.1%), S2:370(25.7%)], ActorGrad: 19384.9062, CriticGrad: 2444.7432, Advantage: μ=-48.044, σ=78.076, range=[-268.04, 221.12]
Batch 5: Reward: -53.2641, Loss: 16622.6289, Revenue: 0.3830, LoadBalance: 0.5527, Tasks: [S0:756(48.2%), S1:358(22.8%), S2:454(29.0%)], ActorGrad: 15086.6953, CriticGrad: 2324.8384, Advantage: μ=-40.865, σ=90.907, range=[-157.82, 234.86]
Epoch 1, Batch 10/32, loss: 11039.212↓, reward: -70.302↓, critic_reward: 0.237, revenue_rate: 0.3645, distance: 6.6705, memory: 0.0680, power: 0.2304, lr: 0.000400, took: 46.994s
Batch 10: Reward: -26.9123, Loss: 17862.6367, Revenue: 0.3879, LoadBalance: 0.6099, Tasks: [S0:745(45.6%), S1:412(25.2%), S2:475(29.1%)], ActorGrad: 15934.1104, CriticGrad: 2271.6238, Advantage: μ=-20.588, σ=100.561, range=[-170.80, 220.18]
Batch 15: Reward: -28.3732, Loss: 9274.1221, Revenue: 0.3232, LoadBalance: 0.6712, Tasks: [S0:558(42.5%), S1:370(28.2%), S2:384(29.3%)], ActorGrad: 9612.0596, CriticGrad: 1591.4111, Advantage: μ=-22.165, σ=72.942, range=[-97.50, 208.96]
Epoch 1, Batch 20/32, loss: 11212.261↓, reward: -29.748↑, critic_reward: 0.070, revenue_rate: 0.3385, distance: 6.0925, memory: 0.0645, power: 0.2132, lr: 0.000400, took: 38.095s
Batch 20: Reward: -8.7731, Loss: 13888.0889, Revenue: 0.3723, LoadBalance: 0.6924, Tasks: [S0:624(41.5%), S1:405(26.9%), S2:475(31.6%)], ActorGrad: 13348.4971, CriticGrad: 1479.7365, Advantage: μ=-6.541, σ=91.091, range=[-102.94, 209.13]
Batch 25: Reward: -10.8036, Loss: 11531.9932, Revenue: 0.3709, LoadBalance: 0.7208, Tasks: [S0:639(39.2%), S1:456(27.9%), S2:537(32.9%)], ActorGrad: 10386.0703, CriticGrad: 1496.3218, Advantage: μ=-8.175, σ=83.499, range=[-71.57, 224.47]
Epoch 1, Batch 30/32, loss: 12868.351↑, reward: -9.143↑, critic_reward: -0.174, revenue_rate: 0.3228, distance: 5.6018, memory: 0.0105, power: 0.2122, lr: 0.000400, took: 37.965s
Batch 30: Reward: -15.9774, Loss: 9756.5449, Revenue: 0.2810, LoadBalance: 0.7276, Tasks: [S0:487(38.0%), S1:388(30.3%), S2:405(31.6%)], ActorGrad: 14011.6729, CriticGrad: 1199.3318, Advantage: μ=-12.219, σ=76.850, range=[-73.24, 236.44]

📊 Epoch 1 训练统计:
  平均奖励: -36.6132
  平均损失: 11419.8239
  平均收益率: 0.3385
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -48.658, revenue_rate: 0.4539, efficiency: 0.2920, distance: 8.5046, memory: 0.1675, power: 0.3213
Test Batch 1/4, reward: -64.677, revenue_rate: 0.4986, efficiency: 0.3934, distance: 10.9768, memory: 0.2588, power: 0.3879
Test Batch 2/4, reward: -28.489, revenue_rate: 0.4120, efficiency: 0.2325, distance: 7.8003, memory: 0.0966, power: 0.2811
Test Batch 3/4, reward: -13.084, revenue_rate: 0.3422, efficiency: 0.1695, distance: 5.5735, memory: -0.2067, power: 0.2484
Test Summary - Avg reward: -45.907±122.454, revenue_rate: 0.4503±0.0642, efficiency: 0.3005, completion_rate: 0.6585, distance: 8.9531, memory: 0.1591, power: 0.3268
Load Balance - Avg balance score: 0.6054±0.2269
Task Distribution by Satellite:
  Satellite 1: 2805 tasks (42.73%)
  Satellite 2: 1690 tasks (25.75%)
  Satellite 3: 2069 tasks (31.52%)
✅ 验证完成 - Epoch 1, reward: -45.907, revenue_rate: 0.4503, distance: 8.9531, memory: 0.1591, power: 0.3268
  ⚠️ 过拟合: 训练验证差距 = 9.2940
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_06_09_50_49 (验证集奖励: -45.9072)

开始训练 Epoch 2/3
Batch 0: Reward: -0.8122, Loss: 12445.7754, Revenue: 0.2982, LoadBalance: 0.7322, Tasks: [S0:479(37.4%), S1:373(29.1%), S2:428(33.4%)], ActorGrad: 13524.2090, CriticGrad: 1301.8618, Advantage: μ=-0.349, σ=86.841, range=[-80.05, 204.07]
Batch 5: Reward: 63.3233, Loss: 27878.3184, Revenue: 0.4324, LoadBalance: 0.7951, Tasks: [S0:722(35.3%), S1:635(31.0%), S2:691(33.7%)], ActorGrad: 22452.4570, CriticGrad: 2391.4348, Advantage: μ=47.587, σ=117.285, range=[-55.67, 234.30]
Epoch 2, Batch 10/32, loss: 18500.663↑, reward: 28.787↑, critic_reward: -0.301, revenue_rate: 0.3763, distance: 7.1998, memory: 0.0505, power: 0.2635, lr: 0.000400, took: 52.703s
Batch 10: Reward: 69.7997, Loss: 28472.7246, Revenue: 0.3562, LoadBalance: 0.7872, Tasks: [S0:566(34.7%), S1:532(32.6%), S2:534(32.7%)], ActorGrad: 23355.9102, CriticGrad: 2269.2788, Advantage: μ=52.389, σ=116.684, range=[-56.50, 223.69]
Batch 15: Reward: 24.0221, Loss: 18637.0098, Revenue: 0.3221, LoadBalance: 0.7604, Tasks: [S0:491(34.9%), S1:478(33.9%), S2:439(31.2%)], ActorGrad: 11650.0371, CriticGrad: 2285.6135, Advantage: μ=18.310, σ=103.047, range=[-61.00, 231.38]
Epoch 2, Batch 20/32, loss: 19467.062↓, reward: 30.424↓, critic_reward: -0.197, revenue_rate: 0.3580, distance: 6.6516, memory: 0.0686, power: 0.2486, lr: 0.000400, took: 44.584s
Batch 20: Reward: 45.6081, Loss: 24329.0000, Revenue: 0.3844, LoadBalance: 0.7859, Tasks: [S0:511(30.1%), S1:565(33.3%), S2:620(36.6%)], ActorGrad: 16912.5957, CriticGrad: 2311.3284, Advantage: μ=34.242, σ=113.566, range=[-56.08, 236.02]
Batch 25: Reward: -15.2445, Loss: 9209.7207, Revenue: 0.3229, LoadBalance: 0.7446, Tasks: [S0:471(33.5%), S1:477(33.9%), S2:460(32.7%)], ActorGrad: 9993.2998, CriticGrad: 1278.5641, Advantage: μ=-11.750, σ=74.901, range=[-58.40, 240.05]
Epoch 2, Batch 30/32, loss: 17522.231↓, reward: 21.103↓, critic_reward: -0.055, revenue_rate: 0.3310, distance: 6.0511, memory: 0.0495, power: 0.2210, lr: 0.000400, took: 43.238s
Batch 30: Reward: -29.2046, Loss: 6081.9697, Revenue: 0.2863, LoadBalance: 0.7190, Tasks: [S0:408(33.6%), S1:395(32.5%), S2:413(34.0%)], ActorGrad: 7022.8936, CriticGrad: 1170.8218, Advantage: μ=-23.470, σ=58.912, range=[-66.85, 207.12]

📊 Epoch 2 训练统计:
  平均奖励: 23.7101
  平均损失: 17861.4981
  平均收益率: 0.3502
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -81.260, revenue_rate: 0.2893, efficiency: 0.0980, distance: 4.7111, memory: 0.0078, power: 0.1676
Test Batch 1/4, reward: -49.168, revenue_rate: 0.2354, efficiency: 0.0705, distance: 4.0325, memory: 0.0179, power: 0.1440
Test Batch 2/4, reward: 6.436, revenue_rate: 0.2997, efficiency: 0.1042, distance: 5.0121, memory: -0.0327, power: 0.1682
Test Batch 3/4, reward: 44.399, revenue_rate: 0.1859, efficiency: 0.0409, distance: 2.6961, memory: -0.1322, power: 0.1053
Test Summary - Avg reward: -37.902±132.692, revenue_rate: 0.2712±0.0499, efficiency: 0.0889, completion_rate: 0.3243, distance: 4.5097, memory: -0.0075, power: 0.1578
Load Balance - Avg balance score: 0.6203±0.1770
Task Distribution by Satellite:
  Satellite 1: 1363 tasks (43.19%)
  Satellite 2: 992 tasks (31.43%)
  Satellite 3: 801 tasks (25.38%)
✅ 验证完成 - Epoch 2, reward: -37.902, revenue_rate: 0.2712, distance: 4.5097, memory: -0.0075, power: 0.1578
  ⚠️ 过拟合: 训练验证差距 = 61.6118
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_06_09_50_49 (验证集奖励: -37.9017)

开始训练 Epoch 3/3
Batch 0: Reward: -25.7288, Loss: 8418.2812, Revenue: 0.2781, LoadBalance: 0.7028, Tasks: [S0:382(32.3%), S1:368(31.1%), S2:434(36.7%)], ActorGrad: 12102.0312, CriticGrad: 1466.4218, Advantage: μ=-20.211, σ=70.124, range=[-128.40, 185.79]
Batch 5: Reward: -1.7042, Loss: 12438.4941, Revenue: 0.3172, LoadBalance: 0.7280, Tasks: [S0:486(34.5%), S1:465(33.0%), S2:457(32.5%)], ActorGrad: 13474.1777, CriticGrad: 1436.6515, Advantage: μ=-1.279, σ=86.810, range=[-64.50, 206.39]
Epoch 3, Batch 10/32, loss: 15774.693↑, reward: 16.064↑, critic_reward: 0.049, revenue_rate: 0.3097, distance: 5.4732, memory: 0.0546, power: 0.2097, lr: 0.000400, took: 49.254s
Batch 10: Reward: -8.6745, Loss: 10844.9902, Revenue: 0.2971, LoadBalance: 0.7307, Tasks: [S0:419(31.9%), S1:435(33.2%), S2:458(34.9%)], ActorGrad: 8799.7432, CriticGrad: 1461.3906, Advantage: μ=-6.670, σ=81.308, range=[-60.41, 234.87]
Batch 15: Reward: 19.5271, Loss: 17518.0664, Revenue: 0.3352, LoadBalance: 0.7158, Tasks: [S0:510(33.2%), S1:526(34.2%), S2:500(32.6%)], ActorGrad: 12790.8115, CriticGrad: 1708.4846, Advantage: μ=14.812, σ=100.596, range=[-73.94, 218.42]
Epoch 3, Batch 20/32, loss: 17177.864↑, reward: 19.952↑, critic_reward: 0.021, revenue_rate: 0.3295, distance: 5.9890, memory: 0.0572, power: 0.2229, lr: 0.000400, took: 43.461s
Batch 20: Reward: 5.4131, Loss: 13838.8076, Revenue: 0.3326, LoadBalance: 0.7602, Tasks: [S0:455(32.3%), S1:485(34.4%), S2:468(33.2%)], ActorGrad: 10079.7822, CriticGrad: 1552.2909, Advantage: μ=4.108, σ=91.077, range=[-59.64, 234.84]
Batch 25: Reward: 18.4720, Loss: 14577.5117, Revenue: 0.3474, LoadBalance: 0.7998, Tasks: [S0:471(33.5%), S1:477(33.9%), S2:460(32.7%)], ActorGrad: 9147.6943, CriticGrad: 1832.6387, Advantage: μ=14.029, σ=92.364, range=[-60.06, 232.75]
Epoch 3, Batch 30/32, loss: 13365.400↓, reward: 3.054↓, critic_reward: 0.063, revenue_rate: 0.3161, distance: 5.5345, memory: 0.0487, power: 0.2087, lr: 0.000400, took: 38.546s
Batch 30: Reward: 77.6425, Loss: 29607.9551, Revenue: 0.3295, LoadBalance: 0.7846, Tasks: [S0:461(32.0%), S1:452(31.4%), S2:527(36.6%)], ActorGrad: 17975.9492, CriticGrad: 1890.9343, Advantage: μ=58.046, σ=116.727, range=[-54.79, 216.08]

📊 Epoch 3 训练统计:
  平均奖励: 14.1995
  平均损失: 15834.1992
  平均收益率: 0.3154
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -403.173, revenue_rate: 0.5093, efficiency: 0.3643, distance: 10.9098, memory: 0.1761, power: 0.3540
Test Batch 1/4, reward: -360.686, revenue_rate: 0.4736, efficiency: 0.3108, distance: 10.3504, memory: 0.1803, power: 0.3211
Test Batch 2/4, reward: -399.209, revenue_rate: 0.5017, efficiency: 0.3451, distance: 10.3283, memory: 0.1531, power: 0.3400
Test Batch 3/4, reward: -441.450, revenue_rate: 0.3129, efficiency: 0.1227, distance: 6.2380, memory: 0.1146, power: 0.2003
Test Summary - Avg reward: -389.840±106.434, revenue_rate: 0.4876±0.0709, efficiency: 0.3314, completion_rate: 0.6747, distance: 10.3578, memory: 0.1676, power: 0.3329
Load Balance - Avg balance score: -0.0824±0.1319
Task Distribution by Satellite:
  Satellite 1: 719 tasks (10.76%)
  Satellite 2: 4996 tasks (74.75%)
  Satellite 3: 969 tasks (14.50%)
✅ 验证完成 - Epoch 3, reward: -389.840, revenue_rate: 0.4876, distance: 10.3578, memory: 0.1676, power: 0.3329
  ⚠️ 过拟合: 训练验证差距 = 404.0393
训练完成

开始测试模型...
Test Batch 0/4, reward: -389.341, revenue_rate: 0.5261, efficiency: 0.3506, distance: 10.3050, memory: 0.0036, power: 0.3411
Test Batch 1/4, reward: -394.500, revenue_rate: 0.4021, efficiency: 0.2070, distance: 7.8326, memory: 0.1087, power: 0.2550
Test Batch 2/4, reward: -396.496, revenue_rate: 0.4780, efficiency: 0.3039, distance: 9.6642, memory: 0.1139, power: 0.3082
Test Batch 3/4, reward: -762.959, revenue_rate: 0.1996, efficiency: 0.0489, distance: 3.2190, memory: 0.0386, power: 0.1177
Test Summary - Avg reward: -408.226±147.772, revenue_rate: 0.4580±0.0909, efficiency: 0.2776, completion_rate: 0.5912, distance: 9.0253, memory: 0.0739, power: 0.2941
Load Balance - Avg balance score: -0.0956±0.1455
Task Distribution by Satellite:
  Satellite 1: 611 tasks (10.43%)
  Satellite 2: 4397 tasks (75.09%)
  Satellite 3: 848 tasks (14.48%)
测试完成 - 平均奖励: -408.226, 平均星座收益率: 0.4580
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_06_09_50_49
   平均奖励: -408.2261
   收益率: 0.4580

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_06_09_59_56
使用模型: gpn_transformer
Actor参数数量: 4,385,924
Critic参数数量: 2,153,729

开始训练 Epoch 1/3
Batch 0: Reward: -83.8465, Loss: 15837.2754, Revenue: 0.4625, LoadBalance: 0.4042, Tasks: [S0:1007(50.0%), S1:428(21.2%), S2:581(28.8%)], ActorGrad: 56648.4375, CriticGrad: 2825.8079, Advantage: μ=-65.638, σ=73.978, range=[-265.43, 117.54]
Batch 5: Reward: -35.0090, Loss: 5583.2964, Revenue: 0.3292, LoadBalance: 0.5633, Tasks: [S0:685(47.6%), S1:368(25.6%), S2:387(26.9%)], ActorGrad: 22379.1055, CriticGrad: 1248.1965, Advantage: μ=-28.487, σ=54.419, range=[-132.02, 134.70]
Epoch 1, Batch 10/32, loss: 7196.861↓, reward: -56.110↑, critic_reward: 0.115, revenue_rate: 0.3755, distance: 7.0028, memory: 0.0518, power: 0.2554, lr: 0.000400, took: 55.533s
Batch 10: Reward: -49.6750, Loss: 4772.8887, Revenue: 0.4070, LoadBalance: 0.5623, Tasks: [S0:897(47.5%), S1:478(25.3%), S2:513(27.2%)], ActorGrad: 24757.3477, CriticGrad: 1013.2484, Advantage: μ=-49.570, σ=48.892, range=[-149.17, 139.50]
Batch 15: Reward: -13.3052, Loss: 5536.6074, Revenue: 0.2879, LoadBalance: 0.6289, Tasks: [S0:534(43.9%), S1:313(25.7%), S2:369(30.3%)], ActorGrad: 17887.8672, CriticGrad: 1297.8127, Advantage: μ=-10.320, σ=59.619, range=[-86.96, 146.90]
Epoch 1, Batch 20/32, loss: 4133.781↓, reward: -28.048↑, critic_reward: -0.336, revenue_rate: 0.3427, distance: 6.2516, memory: 0.0628, power: 0.2329, lr: 0.000400, took: 43.836s
Batch 20: Reward: -19.3686, Loss: 5026.7686, Revenue: 0.3516, LoadBalance: 0.6637, Tasks: [S0:643(41.0%), S1:447(28.5%), S2:478(30.5%)], ActorGrad: 16567.1250, CriticGrad: 1229.7601, Advantage: μ=-15.229, σ=56.110, range=[-131.57, 129.86]
Batch 25: Reward: 2.7542, Loss: 7189.2310, Revenue: 0.3690, LoadBalance: 0.7024, Tasks: [S0:631(38.7%), S1:492(30.1%), S2:509(31.2%)], ActorGrad: 20185.6816, CriticGrad: 1161.3790, Advantage: μ=2.591, σ=67.757, range=[-93.56, 141.45]
Epoch 1, Batch 30/32, loss: 5209.566↑, reward: -12.018↑, critic_reward: -0.523, revenue_rate: 0.3354, distance: 6.0259, memory: 0.0444, power: 0.2255, lr: 0.000400, took: 44.004s
Batch 30: Reward: -22.1272, Loss: 3359.7905, Revenue: 0.3521, LoadBalance: 0.6737, Tasks: [S0:548(37.2%), S1:416(28.3%), S2:508(34.5%)], ActorGrad: 14028.2383, CriticGrad: 638.0035, Advantage: μ=-17.959, σ=45.796, range=[-51.00, 153.67]

📊 Epoch 1 训练统计:
  平均奖励: -29.5341
  平均损失: 5628.7700
  平均收益率: 0.3498
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -185.377, revenue_rate: 0.5015, efficiency: 0.3095, distance: 9.2552, memory: 0.1059, power: 0.3036
Test Batch 1/4, reward: -162.760, revenue_rate: 0.4738, efficiency: 0.2829, distance: 9.3980, memory: 0.1796, power: 0.2941
Test Batch 2/4, reward: -179.652, revenue_rate: 0.4342, efficiency: 0.2290, distance: 8.1270, memory: 0.0751, power: 0.2619
Test Batch 3/4, reward: -160.111, revenue_rate: 0.3774, efficiency: 0.1509, distance: 5.6734, memory: -0.0427, power: 0.1976
Test Summary - Avg reward: -175.297±74.536, revenue_rate: 0.4661±0.0567, efficiency: 0.2689, completion_rate: 0.5732, distance: 8.7966, memory: 0.1137, power: 0.2830
Load Balance - Avg balance score: 0.0549±0.1422
Task Distribution by Satellite:
  Satellite 1: 536 tasks (9.47%)
  Satellite 2: 3887 tasks (68.67%)
  Satellite 3: 1237 tasks (21.86%)
✅ 验证完成 - Epoch 1, reward: -175.297, revenue_rate: 0.4661, distance: 8.7966, memory: 0.1137, power: 0.2830
  ⚠️ 过拟合: 训练验证差距 = 145.7629
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_06_09_59_56 (验证集奖励: -175.2970)

开始训练 Epoch 2/3
Batch 0: Reward: 11.8425, Loss: 6548.9414, Revenue: 0.3297, LoadBalance: 0.7260, Tasks: [S0:513(35.6%), S1:451(31.3%), S2:476(33.1%)], ActorGrad: 15710.6396, CriticGrad: 1298.6671, Advantage: μ=9.881, σ=64.367, range=[-39.84, 138.72]
Batch 5: Reward: 0.2512, Loss: 5529.9658, Revenue: 0.2596, LoadBalance: 0.6741, Tasks: [S0:370(34.0%), S1:341(31.3%), S2:377(34.7%)], ActorGrad: 12933.2861, CriticGrad: 948.4424, Advantage: μ=0.991, σ=60.380, range=[-61.68, 142.91]
Epoch 2, Batch 10/32, loss: 4560.915↓, reward: -9.192↓, critic_reward: -0.876, revenue_rate: 0.3030, distance: 5.4631, memory: 0.0698, power: 0.2030, lr: 0.000400, took: 43.202s
Batch 10: Reward: 3.3473, Loss: 5935.2441, Revenue: 0.2968, LoadBalance: 0.7266, Tasks: [S0:447(34.9%), S1:418(32.7%), S2:415(32.4%)], ActorGrad: 16691.8828, CriticGrad: 880.9114, Advantage: μ=3.463, σ=62.204, range=[-56.00, 146.67]
Batch 15: Reward: -16.2879, Loss: 4742.2861, Revenue: 0.2887, LoadBalance: 0.6660, Tasks: [S0:416(34.2%), S1:396(32.6%), S2:404(33.2%)], ActorGrad: 14998.3691, CriticGrad: 1119.7349, Advantage: μ=-12.571, σ=55.216, range=[-129.38, 145.09]
Epoch 2, Batch 20/32, loss: 5197.683↓, reward: -5.579↓, critic_reward: -0.906, revenue_rate: 0.2877, distance: 4.9413, memory: 0.0368, power: 0.1887, lr: 0.000400, took: 35.431s
Batch 20: Reward: -29.2194, Loss: 3710.5317, Revenue: 0.3105, LoadBalance: 0.6570, Tasks: [S0:423(32.2%), S1:449(34.2%), S2:440(33.5%)], ActorGrad: 14582.6924, CriticGrad: 761.4540, Advantage: μ=-23.611, σ=45.893, range=[-129.40, 127.88]
Batch 25: Reward: 1.6111, Loss: 5702.0142, Revenue: 0.3198, LoadBalance: 0.6916, Tasks: [S0:448(33.3%), S1:455(33.9%), S2:441(32.8%)], ActorGrad: 16241.0117, CriticGrad: 1403.8324, Advantage: μ=2.073, σ=61.172, range=[-58.18, 137.74]
Epoch 2, Batch 30/32, loss: 6007.251↑, reward: -4.071↑, critic_reward: -1.047, revenue_rate: 0.3166, distance: 5.6265, memory: 0.0685, power: 0.2113, lr: 0.000400, took: 39.097s
Batch 30: Reward: -12.3557, Loss: 4679.5801, Revenue: 0.2434, LoadBalance: 0.6737, Tasks: [S0:349(33.0%), S1:353(33.4%), S2:354(33.5%)], ActorGrad: 14842.2930, CriticGrad: 1083.2225, Advantage: μ=-9.180, σ=55.478, range=[-87.76, 131.20]

📊 Epoch 2 训练统计:
  平均奖励: -6.6390
  平均损失: 5176.3232
  平均收益率: 0.3001
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -23.352, revenue_rate: 0.3800, efficiency: 0.1659, distance: 6.7643, memory: 0.0191, power: 0.2115
Test Batch 1/4, reward: -21.558, revenue_rate: 0.2833, efficiency: 0.0905, distance: 4.7368, memory: 0.0542, power: 0.1566
Test Batch 2/4, reward: -23.350, revenue_rate: 0.3225, efficiency: 0.1161, distance: 5.3392, memory: 0.0312, power: 0.1782
Test Batch 3/4, reward: 19.358, revenue_rate: 0.2850, efficiency: 0.1054, distance: 5.3769, memory: -0.0446, power: 0.1754
Test Summary - Avg reward: -21.069±64.023, revenue_rate: 0.3269±0.0563, efficiency: 0.1234, completion_rate: 0.3719, distance: 5.6039, memory: 0.0317, power: 0.1818
Load Balance - Avg balance score: 0.6403±0.1771
Task Distribution by Satellite:
  Satellite 1: 1105 tasks (30.42%)
  Satellite 2: 1599 tasks (44.03%)
  Satellite 3: 928 tasks (25.55%)
✅ 验证完成 - Epoch 2, reward: -21.069, revenue_rate: 0.3269, distance: 5.6039, memory: 0.0317, power: 0.1818
  ⚠️ 过拟合: 训练验证差距 = 14.4298
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_06_09_59_56 (验证集奖励: -21.0688)

开始训练 Epoch 3/3
Batch 0: Reward: -21.8972, Loss: 3856.0239, Revenue: 0.3011, LoadBalance: 0.6604, Tasks: [S0:414(32.3%), S1:421(32.9%), S2:445(34.8%)], ActorGrad: 13260.6777, CriticGrad: 873.6630, Advantage: μ=-17.070, σ=49.146, range=[-119.72, 130.11]
Batch 5: Reward: -27.7937, Loss: 2379.7224, Revenue: 0.2986, LoadBalance: 0.6749, Tasks: [S0:419(32.7%), S1:416(32.5%), S2:445(34.8%)], ActorGrad: 12202.7803, CriticGrad: 638.9210, Advantage: μ=-26.596, σ=41.549, range=[-106.33, 184.06]
Epoch 3, Batch 10/32, loss: 4626.799↑, reward: -9.809↑, critic_reward: -1.212, revenue_rate: 0.3046, distance: 5.3074, memory: 0.0438, power: 0.1998, lr: 0.000400, took: 42.341s
Batch 10: Reward: 1.3465, Loss: 5963.1934, Revenue: 0.3098, LoadBalance: 0.7048, Tasks: [S0:437(34.1%), S1:423(33.0%), S2:420(32.8%)], ActorGrad: 16459.6523, CriticGrad: 1133.1095, Advantage: μ=2.089, σ=62.388, range=[-53.61, 143.88]
Batch 15: Reward: 1.4428, Loss: 5614.0703, Revenue: 0.3355, LoadBalance: 0.7293, Tasks: [S0:471(32.7%), S1:498(34.6%), S2:471(32.7%)], ActorGrad: 13128.3408, CriticGrad: 933.8633, Advantage: μ=2.338, σ=60.748, range=[-64.90, 162.84]
Epoch 3, Batch 20/32, loss: 4860.095↓, reward: -6.429↓, critic_reward: -1.316, revenue_rate: 0.3194, distance: 5.6382, memory: 0.0218, power: 0.2103, lr: 0.000400, took: 40.235s
Batch 20: Reward: 13.7029, Loss: 7979.6309, Revenue: 0.2751, LoadBalance: 0.7213, Tasks: [S0:370(32.1%), S1:394(34.2%), S2:388(33.7%)], ActorGrad: 17526.9102, CriticGrad: 1086.0609, Advantage: μ=11.969, σ=70.096, range=[-74.63, 132.20]
Batch 25: Reward: -12.1490, Loss: 3498.4058, Revenue: 0.2600, LoadBalance: 0.6849, Tasks: [S0:388(34.6%), S1:381(34.0%), S2:351(31.3%)], ActorGrad: 11904.1201, CriticGrad: 1015.2948, Advantage: μ=-8.941, σ=48.857, range=[-45.49, 121.64]
Epoch 3, Batch 30/32, loss: 5200.480↓, reward: -3.586↓, critic_reward: -1.411, revenue_rate: 0.2999, distance: 5.2543, memory: 0.0466, power: 0.1969, lr: 0.000400, took: 37.008s
Batch 30: Reward: 16.9298, Loss: 8187.9302, Revenue: 0.2664, LoadBalance: 0.7007, Tasks: [S0:342(31.4%), S1:358(32.9%), S2:388(35.7%)], ActorGrad: 17680.0664, CriticGrad: 1172.6143, Advantage: μ=14.470, σ=70.499, range=[-62.88, 141.61]

📊 Epoch 3 训练统计:
  平均奖励: -5.7535
  平均损失: 4947.7756
  平均收益率: 0.3051
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -74.655, revenue_rate: 0.3631, efficiency: 0.1550, distance: 6.5942, memory: 0.0512, power: 0.2138
Test Batch 1/4, reward: -69.072, revenue_rate: 0.3155, efficiency: 0.1165, distance: 5.7311, memory: 0.1138, power: 0.1773
Test Batch 2/4, reward: -67.436, revenue_rate: 0.3565, efficiency: 0.1526, distance: 6.4028, memory: 0.0561, power: 0.2115
Test Batch 3/4, reward: -68.607, revenue_rate: 0.2705, efficiency: 0.0866, distance: 4.2370, memory: 0.0674, power: 0.1525
Test Summary - Avg reward: -70.316±43.953, revenue_rate: 0.3421±0.0455, efficiency: 0.1392, completion_rate: 0.4045, distance: 6.1625, memory: 0.0735, power: 0.1990
Load Balance - Avg balance score: 0.4205±0.1967
Task Distribution by Satellite:
  Satellite 1: 937 tasks (23.64%)
  Satellite 2: 855 tasks (21.57%)
  Satellite 3: 2172 tasks (54.79%)
✅ 验证完成 - Epoch 3, reward: -70.316, revenue_rate: 0.3421, distance: 6.1625, memory: 0.0735, power: 0.1990
  ⚠️ 过拟合: 训练验证差距 = 64.5628
训练完成

开始测试模型...
Test Batch 0/4, reward: -76.914, revenue_rate: 0.3780, efficiency: 0.1662, distance: 6.6820, memory: 0.0826, power: 0.2230
Test Batch 1/4, reward: -80.539, revenue_rate: 0.3700, efficiency: 0.1621, distance: 6.2909, memory: 0.0900, power: 0.2165
Test Batch 2/4, reward: -78.521, revenue_rate: 0.3521, efficiency: 0.1475, distance: 6.2465, memory: 0.1120, power: 0.2035
Test Batch 3/4, reward: -71.530, revenue_rate: 0.2853, efficiency: 0.0970, distance: 5.1062, memory: 0.0879, power: 0.1642
Test Summary - Avg reward: -78.373±38.859, revenue_rate: 0.3635±0.0428, efficiency: 0.1562, completion_rate: 0.4285, distance: 6.3545, memory: 0.0946, power: 0.2123
Load Balance - Avg balance score: 0.3835±0.1823
Task Distribution by Satellite:
  Satellite 1: 958 tasks (22.83%)
  Satellite 2: 877 tasks (20.90%)
  Satellite 3: 2361 tasks (56.27%)
测试完成 - 平均奖励: -78.373, 平均星座收益率: 0.3635
✅ 模式 COMPETITIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_06_09_59_56
   平均奖励: -78.3729
   收益率: 0.3635

🚀 [3/3] 开始训练模式: HYBRID

============================================================
开始训练星座模式: HYBRID
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: hybrid
verbose: True
2025_08_06_10_08_29
使用模型: gpn_transformer
Actor参数数量: 4,385,924
Critic参数数量: 2,153,729

开始训练 Epoch 1/3
Batch 0: Reward: -61.6514, Loss: 13696.6455, Revenue: 0.3593, LoadBalance: 0.5635, Tasks: [S0:694(48.2%), S1:376(26.1%), S2:370(25.7%)], ActorGrad: 19384.9062, CriticGrad: 2444.7432, Advantage: μ=-48.044, σ=78.076, range=[-268.04, 221.12]
Batch 5: Reward: -53.2641, Loss: 16622.6289, Revenue: 0.3830, LoadBalance: 0.5527, Tasks: [S0:756(48.2%), S1:358(22.8%), S2:454(29.0%)], ActorGrad: 15086.6943, CriticGrad: 2324.8391, Advantage: μ=-40.865, σ=90.907, range=[-157.82, 234.86]
Epoch 1, Batch 10/32, loss: 10502.067↓, reward: -68.046↑, critic_reward: 0.228, revenue_rate: 0.3638, distance: 6.6922, memory: 0.0744, power: 0.2314, lr: 0.000400, took: 48.161s
Batch 10: Reward: -46.8900, Loss: 14675.4258, Revenue: 0.3332, LoadBalance: 0.5929, Tasks: [S0:628(45.6%), S1:325(23.6%), S2:423(30.7%)], ActorGrad: 14058.3633, CriticGrad: 1659.9664, Advantage: μ=-36.047, σ=86.893, range=[-156.64, 231.50]
Batch 15: Reward: -9.8170, Loss: 14919.3555, Revenue: 0.4118, LoadBalance: 0.6776, Tasks: [S0:742(42.9%), S1:458(26.5%), S2:528(30.6%)], ActorGrad: 13907.7646, CriticGrad: 1816.9479, Advantage: μ=-7.550, σ=94.082, range=[-77.76, 227.61]
Epoch 1, Batch 20/32, loss: 11419.780↓, reward: -32.864↑, critic_reward: 0.074, revenue_rate: 0.3758, distance: 6.9251, memory: 0.0686, power: 0.2436, lr: 0.000400, took: 44.953s
Batch 20: Reward: -33.3372, Loss: 7178.3574, Revenue: 0.3641, LoadBalance: 0.6920, Tasks: [S0:636(42.3%), S1:421(28.0%), S2:447(29.7%)], ActorGrad: 11850.6924, CriticGrad: 1148.8260, Advantage: μ=-26.404, σ=62.930, range=[-89.00, 215.61]
Batch 25: Reward: 0.0910, Loss: 15626.7168, Revenue: 0.3369, LoadBalance: 0.6882, Tasks: [S0:536(39.9%), S1:400(29.8%), S2:408(30.4%)], ActorGrad: 14458.8008, CriticGrad: 1896.0261, Advantage: μ=0.145, σ=96.405, range=[-85.34, 260.08]
Epoch 1, Batch 30/32, loss: 13876.519↑, reward: -2.849↑, critic_reward: -0.083, revenue_rate: 0.3297, distance: 5.7514, memory: 0.0263, power: 0.2109, lr: 0.000400, took: 38.943s
Batch 30: Reward: 2.2549, Loss: 15823.0801, Revenue: 0.3347, LoadBalance: 0.7355, Tasks: [S0:509(36.2%), S1:434(30.8%), S2:465(33.0%)], ActorGrad: 10843.1670, CriticGrad: 1867.1156, Advantage: μ=1.829, σ=96.945, range=[-117.28, 233.79]

📊 Epoch 1 训练统计:
  平均奖励: -31.1336
  平均损失: 12514.0519
  平均收益率: 0.3530
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -3.305, revenue_rate: 0.3358, efficiency: 0.1554, distance: 5.8079, memory: 0.1112, power: 0.2385
Test Batch 1/4, reward: -1.844, revenue_rate: 0.3134, efficiency: 0.1384, distance: 5.7166, memory: 0.1521, power: 0.2184
Test Batch 2/4, reward: -20.196, revenue_rate: 0.3029, efficiency: 0.1273, distance: 5.2711, memory: 0.1264, power: 0.2181
Test Batch 3/4, reward: -52.207, revenue_rate: 0.2541, efficiency: 0.0949, distance: 3.7246, memory: 0.1067, power: 0.1790
Test Summary - Avg reward: -10.199±121.074, revenue_rate: 0.3148±0.0508, efficiency: 0.1385, completion_rate: 0.4382, distance: 5.5236, memory: 0.1290, power: 0.2232
Load Balance - Avg balance score: 0.6744±0.1643
Task Distribution by Satellite:
  Satellite 1: 1213 tasks (27.74%)
  Satellite 2: 1768 tasks (40.44%)
  Satellite 3: 1391 tasks (31.82%)
✅ 验证完成 - Epoch 1, reward: -10.199, revenue_rate: 0.3148, distance: 5.5236, memory: 0.1290, power: 0.2232
  ⚠️ 欠拟合: 训练验证差距 = -20.9349
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_06_10_08_29 (验证集奖励: -10.1987)

开始训练 Epoch 2/3
Batch 0: Reward: -35.3448, Loss: 8519.2041, Revenue: 0.2981, LoadBalance: 0.6677, Tasks: [S0:491(39.3%), S1:335(26.8%), S2:422(33.8%)], ActorGrad: 8310.7891, CriticGrad: 1150.8447, Advantage: μ=-27.650, σ=68.195, range=[-121.39, 221.51]
Batch 5: Reward: 24.8600, Loss: 17803.9336, Revenue: 0.3877, LoadBalance: 0.7532, Tasks: [S0:621(35.3%), S1:534(30.3%), S2:605(34.4%)], ActorGrad: 16696.8613, CriticGrad: 1945.6117, Advantage: μ=18.879, σ=100.721, range=[-65.38, 236.63]
Epoch 2, Batch 10/32, loss: 13987.091↑, reward: 1.541↑, critic_reward: -0.143, revenue_rate: 0.3387, distance: 6.2134, memory: 0.0619, power: 0.2255, lr: 0.000400, took: 46.828s
Batch 10: Reward: 10.9899, Loss: 15350.3359, Revenue: 0.4068, LoadBalance: 0.7605, Tasks: [S0:553(32.0%), S1:566(32.8%), S2:609(35.2%)], ActorGrad: 10499.0352, CriticGrad: 1635.7413, Advantage: μ=8.574, σ=95.249, range=[-79.01, 225.83]
Batch 15: Reward: -12.1271, Loss: 9507.3525, Revenue: 0.3148, LoadBalance: 0.7628, Tasks: [S0:402(32.2%), S1:396(31.7%), S2:450(36.1%)], ActorGrad: 7118.9995, CriticGrad: 1170.1412, Advantage: μ=-9.340, σ=76.316, range=[-84.83, 221.47]
Epoch 2, Batch 20/32, loss: 14398.347↓, reward: 5.649↓, critic_reward: -0.166, revenue_rate: 0.3329, distance: 5.8784, memory: 0.0421, power: 0.2145, lr: 0.000400, took: 39.615s
Batch 20: Reward: 32.0926, Loss: 20807.6641, Revenue: 0.2854, LoadBalance: 0.7644, Tasks: [S0:373(31.5%), S1:386(32.6%), S2:425(35.9%)], ActorGrad: 12692.3887, CriticGrad: 1650.5756, Advantage: μ=24.130, σ=107.523, range=[-100.80, 227.69]
Batch 25: Reward: 4.8911, Loss: 15214.9297, Revenue: 0.3074, LoadBalance: 0.7092, Tasks: [S0:426(33.3%), S1:420(32.8%), S2:434(33.9%)], ActorGrad: 10373.5752, CriticGrad: 1604.3302, Advantage: μ=3.774, σ=95.155, range=[-69.94, 228.33]
Epoch 2, Batch 30/32, loss: 16897.130↑, reward: 13.913↑, critic_reward: -0.104, revenue_rate: 0.3062, distance: 5.4137, memory: 0.0416, power: 0.1941, lr: 0.000400, took: 35.933s
Batch 30: Reward: -9.2130, Loss: 11136.8086, Revenue: 0.3497, LoadBalance: 0.7351, Tasks: [S0:457(31.0%), S1:511(34.7%), S2:504(34.2%)], ActorGrad: 8782.0918, CriticGrad: 1543.4790, Advantage: μ=-7.118, σ=82.265, range=[-63.11, 236.17]

📊 Epoch 2 训练统计:
  平均奖励: 4.8893
  平均损失: 14566.6550
  平均收益率: 0.3253
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -137.095, revenue_rate: 0.3451, efficiency: 0.1475, distance: 6.2772, memory: 0.0582, power: 0.2062
Test Batch 1/4, reward: -129.000, revenue_rate: 0.3554, efficiency: 0.1554, distance: 6.7174, memory: 0.0813, power: 0.2164
Test Batch 2/4, reward: -156.018, revenue_rate: 0.4093, efficiency: 0.2117, distance: 8.1536, memory: 0.0970, power: 0.2605
Test Batch 3/4, reward: -118.882, revenue_rate: 0.2154, efficiency: 0.0517, distance: 3.1983, memory: -0.1128, power: 0.1159
Test Summary - Avg reward: -139.832±74.654, revenue_rate: 0.3638±0.0558, efficiency: 0.1667, completion_rate: 0.4517, distance: 6.8954, memory: 0.0711, power: 0.2232
Load Balance - Avg balance score: 0.3383±0.1848
Task Distribution by Satellite:
  Satellite 1: 738 tasks (16.61%)
  Satellite 2: 1159 tasks (26.08%)
  Satellite 3: 2547 tasks (57.31%)
✅ 验证完成 - Epoch 2, reward: -139.832, revenue_rate: 0.3638, distance: 6.8954, memory: 0.0711, power: 0.2232
  ⚠️ 过拟合: 训练验证差距 = 144.7211

开始训练 Epoch 3/3
Batch 0: Reward: 20.8960, Loss: 16558.0664, Revenue: 0.3186, LoadBalance: 0.7338, Tasks: [S0:373(30.7%), S1:409(33.6%), S2:434(35.7%)], ActorGrad: 8688.6514, CriticGrad: 1619.7505, Advantage: μ=15.919, σ=97.789, range=[-76.46, 193.41]
Batch 5: Reward: -6.0552, Loss: 12259.6660, Revenue: 0.3173, LoadBalance: 0.7370, Tasks: [S0:443(33.8%), S1:454(34.6%), S2:415(31.6%)], ActorGrad: 8794.7080, CriticGrad: 1678.9382, Advantage: μ=-4.486, σ=86.137, range=[-68.51, 232.57]
Epoch 3, Batch 10/32, loss: 17442.964↑, reward: 18.940↑, critic_reward: -0.101, revenue_rate: 0.3093, distance: 5.4163, memory: 0.0320, power: 0.1921, lr: 0.000400, took: 40.431s
Batch 10: Reward: 36.2786, Loss: 19389.6465, Revenue: 0.3745, LoadBalance: 0.7700, Tasks: [S0:493(32.1%), S1:531(34.6%), S2:512(33.3%)], ActorGrad: 8971.8506, CriticGrad: 1900.7527, Advantage: μ=27.431, σ=103.101, range=[-59.86, 218.93]
Batch 15: Reward: 10.3231, Loss: 16392.5781, Revenue: 0.3109, LoadBalance: 0.7680, Tasks: [S0:394(34.2%), S1:406(35.2%), S2:352(30.6%)], ActorGrad: 7969.8823, CriticGrad: 2033.5205, Advantage: μ=7.869, σ=98.258, range=[-72.22, 260.81]
Epoch 3, Batch 20/32, loss: 16902.526↓, reward: 16.024↓, critic_reward: -0.065, revenue_rate: 0.3062, distance: 5.3810, memory: 0.0417, power: 0.1828, lr: 0.000400, took: 34.089s
Batch 20: Reward: 1.8969, Loss: 15235.4932, Revenue: 0.2868, LoadBalance: 0.7125, Tasks: [S0:364(32.5%), S1:393(35.1%), S2:363(32.4%)], ActorGrad: 7921.4629, CriticGrad: 1619.4705, Advantage: μ=1.491, σ=95.274, range=[-133.45, 227.47]
Batch 25: Reward: -6.9836, Loss: 11929.2373, Revenue: 0.3125, LoadBalance: 0.7325, Tasks: [S0:389(31.2%), S1:439(35.2%), S2:420(33.7%)], ActorGrad: 8050.9399, CriticGrad: 1711.4800, Advantage: μ=-5.410, σ=85.016, range=[-65.67, 214.71]
Epoch 3, Batch 30/32, loss: 14339.279↑, reward: 2.963↑, critic_reward: 0.032, revenue_rate: 0.2905, distance: 4.9342, memory: 0.0226, power: 0.1748, lr: 0.000400, took: 34.787s
Batch 30: Reward: 26.1602, Loss: 16325.7441, Revenue: 0.2895, LoadBalance: 0.7584, Tasks: [S0:366(33.6%), S1:353(32.4%), S2:369(33.9%)], ActorGrad: 8319.7520, CriticGrad: 1236.1924, Advantage: μ=19.885, σ=96.441, range=[-49.42, 223.56]

📊 Epoch 3 训练统计:
  平均奖励: 13.2500
  平均损失: 16442.6033
  平均收益率: 0.3012
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -241.968, revenue_rate: 0.3639, efficiency: 0.1663, distance: 6.8307, memory: 0.1050, power: 0.2253
Test Batch 1/4, reward: -256.300, revenue_rate: 0.3023, efficiency: 0.1114, distance: 5.3681, memory: 0.1214, power: 0.1783
Test Batch 2/4, reward: -259.605, revenue_rate: 0.3239, efficiency: 0.1290, distance: 5.6786, memory: 0.0580, power: 0.1944
Test Batch 3/4, reward: -391.686, revenue_rate: 0.2890, efficiency: 0.0997, distance: 5.1588, memory: 0.0840, power: 0.1665
Test Summary - Avg reward: -258.187±116.271, revenue_rate: 0.3284±0.0496, efficiency: 0.1341, completion_rate: 0.4053, distance: 5.9271, memory: 0.0944, power: 0.1980
Load Balance - Avg balance score: 0.1040±0.1875
Task Distribution by Satellite:
  Satellite 1: 2670 tasks (67.15%)
  Satellite 2: 703 tasks (17.68%)
  Satellite 3: 603 tasks (15.17%)
✅ 验证完成 - Epoch 3, reward: -258.187, revenue_rate: 0.3284, distance: 5.9271, memory: 0.0944, power: 0.1980
  ⚠️ 过拟合: 训练验证差距 = 271.4367
训练完成

开始测试模型...
Test Batch 0/4, reward: -248.986, revenue_rate: 0.3392, efficiency: 0.1354, distance: 5.8814, memory: 0.0450, power: 0.2037
Test Batch 1/4, reward: -238.513, revenue_rate: 0.2824, efficiency: 0.0957, distance: 4.9108, memory: 0.0841, power: 0.1626
Test Batch 2/4, reward: -238.712, revenue_rate: 0.2854, efficiency: 0.0994, distance: 5.0604, memory: 0.0960, power: 0.1688
Test Batch 3/4, reward: -150.917, revenue_rate: 0.2228, efficiency: 0.0686, distance: 4.4107, memory: 0.1487, power: 0.1423
Test Summary - Avg reward: -238.424±125.402, revenue_rate: 0.2991±0.0456, efficiency: 0.1085, completion_rate: 0.3598, distance: 5.2493, memory: 0.0780, power: 0.1769
Load Balance - Avg balance score: 0.1443±0.2098
Task Distribution by Satellite:
  Satellite 1: 2306 tasks (65.66%)
  Satellite 2: 646 tasks (18.39%)
  Satellite 3: 560 tasks (15.95%)
测试完成 - 平均奖励: -238.424, 平均星座收益率: 0.2991
✅ 模式 HYBRID 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_06_10_08_29
   平均奖励: -238.4241
   收益率: 0.2991

================================================================================
🎯 多模式训练总结
================================================================================
✅ COOPERATIVE: 奖励=-408.2261, 收益率=0.4580
✅ COMPETITIVE: 奖励=-78.3729, 收益率=0.3635
✅ HYBRID: 奖励=-238.4241, 收益率=0.2991

🏆 最佳模式: COMPETITIVE
   最高奖励: -78.3729
   对应收益率: 0.3635
   模型路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_06_09_59_56

🎉 所有模式训练完成！
