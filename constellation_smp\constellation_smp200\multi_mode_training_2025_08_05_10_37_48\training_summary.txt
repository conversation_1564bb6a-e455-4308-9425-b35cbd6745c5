🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp200\multi_mode_training_2025_08_05_10_37_48
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 200
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 16
seed: 12346
train-size: 1000
valid-size: 100
epochs: 2
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_05_10_37_55
使用模型: gpn_transformer
Actor参数数量: 5,929,732
Critic参数数量: 2,942,081

开始训练 Epoch 1/2
Batch 0: Reward: 22.0687, Loss: 39523.1641, Revenue: 0.1743, LoadBalance: 0.6892, Tasks: [S0:221(31.4%), S1:224(31.8%), S2:259(36.8%)], ActorGrad: 5462.0347, CriticGrad: 4317.7456, Advantage: μ=1.088, σ=10.000, range=[-7.87, 18.77]
Batch 5: Reward: -1325.6624, Loss: 1756392.2500, Revenue: 0.5466, LoadBalance: 0.0000, Tasks: [S0:86(3.7%), S1:53(2.3%), S2:2165(94.0%)], ActorGrad: 841698.3125, CriticGrad: 33862.9531, Advantage: μ=-1323.288, σ=75.191, range=[-1451.53, -1147.72]
Epoch 1, Batch 10/63, loss: 880269.271↑, reward: -756.372↓, critic_reward: -1.624, revenue_rate: 0.4389, distance: 16.8537, memory: 0.3507, power: 0.5767, lr: 0.000400, took: 60.981s
Batch 10: Reward: -644.6966, Loss: 501448.2500, Revenue: 0.5597, LoadBalance: 0.0000, Tasks: [S0:1916(74.8%), S1:68(2.7%), S2:576(22.5%)], ActorGrad: 13293.5527, CriticGrad: 10379.8574, Advantage: μ=-20.560, σ=10.000, range=[-57.07, -13.42]
Batch 15: Reward: -2038.7378, Loss: 4431694.0000, Revenue: 0.6992, LoadBalance: 0.0000, Tasks: [S0:2961(97.9%), S1:47(1.6%), S2:16(0.5%)], ActorGrad: 29806.2012, CriticGrad: 28658.2207, Advantage: μ=-36.326, σ=10.000, range=[-46.84, -23.98]
Epoch 1, Batch 20/63, loss: 2998778.875↑, reward: -1623.927↓, critic_reward: -4.600, revenue_rate: 0.6484, distance: 25.5659, memory: 0.5806, power: 0.8848, lr: 0.000400, took: 104.909s
Batch 20: Reward: -2141.0229, Loss: 4968902.0000, Revenue: 0.7415, LoadBalance: 0.0000, Tasks: [S0:3141(98.6%), S1:18(0.6%), S2:25(0.8%)], ActorGrad: 25929.3691, CriticGrad: 29966.6758, Advantage: μ=-32.432, σ=10.000, range=[-55.35, -22.56]
Batch 25: Reward: -1418.7351, Loss: 2001955.7500, Revenue: 0.5745, LoadBalance: 0.0000, Tasks: [S0:2498(95.8%), S1:68(2.6%), S2:42(1.6%)], ActorGrad: 908793.6250, CriticGrad: 20510.8047, Advantage: μ=-1413.146, σ=72.833, range=[-1503.44, -1260.47]
Epoch 1, Batch 30/63, loss: 3061076.038↓, reward: -1676.888↑, critic_reward: -5.344, revenue_rate: 0.6274, distance: 24.7529, memory: 0.5002, power: 0.8798, lr: 0.000400, took: 106.400s
Batch 30: Reward: -1372.1544, Loss: 1949306.0000, Revenue: 0.5158, LoadBalance: 0.0000, Tasks: [S0:2321(93.6%), S1:35(1.4%), S2:124(5.0%)], ActorGrad: 27181.0586, CriticGrad: 18971.5879, Advantage: μ=-46.395, σ=10.000, range=[-82.59, -38.93]
Batch 35: Reward: -462.5875, Loss: 287580.7188, Revenue: 0.5097, LoadBalance: 0.0420, Tasks: [S0:674(29.7%), S1:59(2.6%), S2:1539(67.7%)], ActorGrad: 13493.4854, CriticGrad: 6544.8125, Advantage: μ=-15.754, σ=10.000, range=[-51.32, -9.96]
Epoch 1, Batch 40/63, loss: 792625.576↓, reward: -800.888↑, critic_reward: -5.689, revenue_rate: 0.4998, distance: 19.8815, memory: 0.4661, power: 0.7147, lr: 0.000400, took: 74.460s
Batch 40: Reward: -1109.5295, Loss: 1336575.0000, Revenue: 0.5626, LoadBalance: 0.0000, Tasks: [S0:270(10.6%), S1:78(3.1%), S2:2196(86.3%)], ActorGrad: 19673.5820, CriticGrad: 15936.5596, Advantage: μ=-31.007, σ=10.000, range=[-56.71, -21.79]
Batch 45: Reward: -399.9629, Loss: 169102.0938, Revenue: 0.3015, LoadBalance: 0.0844, Tasks: [S0:111(8.8%), S1:283(22.4%), S2:870(68.8%)], ActorGrad: 12121.7783, CriticGrad: 5492.8325, Advantage: μ=-32.399, σ=10.000, range=[-47.48, -19.89]
Epoch 1, Batch 50/63, loss: 650098.376↓, reward: -675.566↑, critic_reward: -5.952, revenue_rate: 0.3908, distance: 14.8233, memory: 0.3073, power: 0.5375, lr: 0.000400, took: 56.249s
Batch 50: Reward: -505.8693, Loss: 255578.7812, Revenue: 0.3872, LoadBalance: 0.0123, Tasks: [S0:115(7.0%), S1:1202(72.9%), S2:331(20.1%)], ActorGrad: 183437.7500, CriticGrad: 6891.9048, Advantage: μ=-499.719, σ=79.058, range=[-676.10, -347.36]
Batch 55: Reward: -363.7005, Loss: 137454.8125, Revenue: 0.3634, LoadBalance: 0.0954, Tasks: [S0:117(7.5%), S1:1039(66.9%), S2:396(25.5%)], ActorGrad: 14900.1523, CriticGrad: 5095.9717, Advantage: μ=-34.923, σ=10.000, range=[-52.18, -22.32]
Epoch 1, Batch 60/63, loss: 304109.433↑, reward: -522.332↓, critic_reward: -6.286, revenue_rate: 0.3412, distance: 12.7120, memory: 0.2920, power: 0.4664, lr: 0.000400, took: 52.070s
Batch 60: Reward: -645.8480, Loss: 423338.1875, Revenue: 0.4036, LoadBalance: 0.0048, Tasks: [S0:125(6.9%), S1:284(15.7%), S2:1399(77.4%)], ActorGrad: 22069.9863, CriticGrad: 9420.4102, Advantage: μ=-50.902, σ=10.000, range=[-66.59, -29.88]

📊 Epoch 1 训练统计:
  平均奖励: -980.1614
  平均损失: 1388697.8721
  平均收益率: 0.4831
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: -260.196, revenue_rate: 0.1987, efficiency: 0.0417, distance: 6.5632, memory: 0.0972, power: 0.2083
Test Batch 1/7, reward: -213.269, revenue_rate: 0.2756, efficiency: 0.0867, distance: 9.5546, memory: 0.1640, power: 0.3019
Test Batch 2/7, reward: -189.783, revenue_rate: 0.2339, efficiency: 0.0585, distance: 7.5359, memory: 0.0928, power: 0.2399
Test Batch 3/7, reward: -193.333, revenue_rate: 0.2171, efficiency: 0.0519, distance: 7.4343, memory: 0.1791, power: 0.2332
Test Batch 4/7, reward: -171.878, revenue_rate: 0.2521, efficiency: 0.0717, distance: 9.0548, memory: 0.1752, power: 0.2864
Test Batch 5/7, reward: -190.045, revenue_rate: 0.2115, efficiency: 0.0505, distance: 7.7603, memory: 0.1600, power: 0.2499
Test Batch 6/7, reward: -154.877, revenue_rate: 0.1974, efficiency: 0.0434, distance: 6.7667, memory: 0.1301, power: 0.2125
Test Summary - Avg reward: -201.156±101.954, revenue_rate: 0.2301±0.0352, efficiency: 0.0595, completion_rate: 0.2547, distance: 7.9351, memory: 0.1441, power: 0.2516
Load Balance - Avg balance score: 0.3482±0.1853
Task Distribution by Satellite:
  Satellite 1: 928 tasks (18.55%)
  Satellite 2: 2880 tasks (57.55%)
  Satellite 3: 1196 tasks (23.90%)
✅ 验证完成 - Epoch 1, reward: -201.156, revenue_rate: 0.2301, distance: 7.9351, memory: 0.1441, power: 0.2516
  ⚠️ 欠拟合: 训练验证差距 = -779.0055
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_05_10_37_55 (验证集奖励: -201.1559)

开始训练 Epoch 2/2
Batch 0: Reward: -150.3538, Loss: 24991.3086, Revenue: 0.1918, LoadBalance: 0.4310, Tasks: [S0:161(18.6%), S1:467(54.1%), S2:236(27.3%)], ActorGrad: 50824.4336, CriticGrad: 2015.2677, Advantage: μ=-143.914, σ=67.567, range=[-358.85, -57.39]
Batch 5: Reward: -811.3602, Loss: 717674.3125, Revenue: 0.5299, LoadBalance: 0.0000, Tasks: [S0:1989(80.2%), S1:450(18.1%), S2:41(1.7%)], ActorGrad: 21812.5488, CriticGrad: 11556.8125, Advantage: μ=-29.376, σ=10.000, range=[-64.88, -20.87]
Epoch 2, Batch 10/63, loss: 651395.446↑, reward: -648.143↓, critic_reward: -6.650, revenue_rate: 0.4470, distance: 17.2494, memory: 0.3693, power: 0.6491, lr: 0.000400, took: 79.202s
Batch 10: Reward: -849.1746, Loss: 854754.4375, Revenue: 0.5477, LoadBalance: 0.0000, Tasks: [S0:1971(79.5%), S1:489(19.7%), S2:20(0.8%)], ActorGrad: 19029.4102, CriticGrad: 12128.0000, Advantage: μ=-21.410, σ=10.000, range=[-47.64, -13.36]
Batch 15: Reward: -569.2970, Loss: 445607.9062, Revenue: 0.5897, LoadBalance: 0.0172, Tasks: [S0:1939(69.2%), S1:831(29.7%), S2:30(1.1%)], ActorGrad: 15911.6260, CriticGrad: 7857.4658, Advantage: μ=-15.163, σ=10.000, range=[-41.34, -7.06]
Epoch 2, Batch 20/63, loss: 650319.998↓, reward: -666.798↑, critic_reward: -6.897, revenue_rate: 0.5699, distance: 22.5653, memory: 0.4699, power: 0.8467, lr: 0.000400, took: 92.657s
Batch 20: Reward: -698.4871, Loss: 551940.2500, Revenue: 0.5319, LoadBalance: 0.0001, Tasks: [S0:557(21.4%), S1:1998(76.6%), S2:53(2.0%)], ActorGrad: 21884.5859, CriticGrad: 9834.4473, Advantage: μ=-24.652, σ=10.000, range=[-58.50, -15.19]
Batch 25: Reward: -354.4763, Loss: 124544.2656, Revenue: 0.4581, LoadBalance: 0.0655, Tasks: [S0:661(30.4%), S1:1432(65.8%), S2:83(3.8%)], ActorGrad: 189090.6875, CriticGrad: 5147.0996, Advantage: μ=-347.207, σ=65.250, range=[-459.94, -236.92]
Epoch 2, Batch 30/63, loss: 339081.447↓, reward: -522.160↑, critic_reward: -7.062, revenue_rate: 0.4469, distance: 17.5973, memory: 0.3729, power: 0.6624, lr: 0.000400, took: 79.243s
Batch 30: Reward: -525.5797, Loss: 283870.9688, Revenue: 0.3079, LoadBalance: 0.0215, Tasks: [S0:1013(73.6%), S1:276(20.1%), S2:87(6.3%)], ActorGrad: 16795.3496, CriticGrad: 7417.5327, Advantage: μ=-40.904, σ=10.000, range=[-60.33, -23.70]
Batch 35: Reward: -27.4340, Loss: 30803.6914, Revenue: 0.1932, LoadBalance: 0.6542, Tasks: [S0:203(23.5%), S1:301(34.8%), S2:360(41.7%)], ActorGrad: 3001.9370, CriticGrad: 451.7589, Advantage: μ=-1.105, σ=10.000, range=[-8.58, 27.51]
Epoch 2, Batch 40/63, loss: 56578.308↓, reward: -135.862↑, critic_reward: -7.384, revenue_rate: 0.2120, distance: 7.3637, memory: 0.1471, power: 0.2987, lr: 0.000400, took: 32.892s
Batch 40: Reward: -52.0017, Loss: 15722.7031, Revenue: 0.2277, LoadBalance: 0.6823, Tasks: [S0:409(39.3%), S1:369(35.5%), S2:262(25.2%)], ActorGrad: 2183.9980, CriticGrad: 785.1703, Advantage: μ=-3.681, σ=10.000, range=[-9.94, 33.21]
Batch 45: Reward: -56.7315, Loss: 12843.1562, Revenue: 0.1740, LoadBalance: 0.6639, Tasks: [S0:189(22.7%), S1:352(42.3%), S2:291(35.0%)], ActorGrad: 2300.2109, CriticGrad: 729.0687, Advantage: μ=-4.660, σ=10.000, range=[-13.18, 32.07]
Epoch 2, Batch 50/63, loss: 19723.052↑, reward: -50.217↓, critic_reward: -7.445, revenue_rate: 0.1824, distance: 6.2565, memory: 0.1592, power: 0.2655, lr: 0.000400, took: 30.481s
Batch 50: Reward: -54.3282, Loss: 14364.0234, Revenue: 0.1535, LoadBalance: 0.6608, Tasks: [S0:338(44.9%), S1:195(25.9%), S2:219(29.1%)], ActorGrad: 3033.8032, CriticGrad: 759.2720, Advantage: μ=-4.101, σ=10.000, range=[-11.71, 32.51]
Batch 55: Reward: 45.8194, Loss: 43782.3438, Revenue: 0.1484, LoadBalance: 0.7588, Tasks: [S0:213(30.3%), S1:240(34.1%), S2:251(35.7%)], ActorGrad: 2390.1411, CriticGrad: 1004.5707, Advantage: μ=2.547, σ=10.000, range=[-6.32, 21.40]
Epoch 2, Batch 60/63, loss: 29353.043↓, reward: -3.981↓, critic_reward: -7.498, revenue_rate: 0.1667, distance: 5.6552, memory: 0.1570, power: 0.2405, lr: 0.000400, took: 30.599s
Batch 60: Reward: 21.9204, Loss: 39341.1484, Revenue: 0.1953, LoadBalance: 0.6848, Tasks: [S0:388(44.1%), S1:260(29.5%), S2:232(26.4%)], ActorGrad: 1992.6118, CriticGrad: 652.1342, Advantage: μ=1.439, σ=10.000, range=[-6.24, 20.13]

📊 Epoch 2 训练统计:
  平均奖励: -320.6639
  平均损失: 279117.5315
  平均收益率: 0.3293
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: -112.877, revenue_rate: 0.2377, efficiency: 0.0642, distance: 8.4136, memory: 0.1374, power: 0.2644
Test Batch 1/7, reward: -43.300, revenue_rate: 0.2137, efficiency: 0.0488, distance: 7.0184, memory: 0.1450, power: 0.2265
Test Batch 2/7, reward: -34.184, revenue_rate: 0.2152, efficiency: 0.0515, distance: 7.0465, memory: 0.1386, power: 0.2300
Test Batch 3/7, reward: -91.583, revenue_rate: 0.1801, efficiency: 0.0360, distance: 5.7796, memory: 0.1227, power: 0.1956
Test Batch 4/7, reward: -91.482, revenue_rate: 0.1807, efficiency: 0.0361, distance: 6.1282, memory: 0.1589, power: 0.1937
Test Batch 5/7, reward: -94.202, revenue_rate: 0.2416, efficiency: 0.0628, distance: 8.2362, memory: 0.1239, power: 0.2592
Test Batch 6/7, reward: -84.870, revenue_rate: 0.1547, efficiency: 0.0263, distance: 5.3624, memory: 0.0789, power: 0.1560
Test Summary - Avg reward: -78.215±95.817, revenue_rate: 0.2092±0.0339, efficiency: 0.0490, completion_rate: 0.2303, distance: 7.0341, memory: 0.1354, power: 0.2253
Load Balance - Avg balance score: 0.6135±0.1548
Task Distribution by Satellite:
  Satellite 1: 965 tasks (21.37%)
  Satellite 2: 1621 tasks (35.89%)
  Satellite 3: 1930 tasks (42.74%)
✅ 验证完成 - Epoch 2, reward: -78.215, revenue_rate: 0.2092, distance: 7.0341, memory: 0.1354, power: 0.2253
  ⚠️ 欠拟合: 训练验证差距 = -242.4485
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_05_10_37_55 (验证集奖励: -78.2154)
训练完成

开始测试模型...
Test Batch 0/7, reward: -86.201, revenue_rate: 0.2158, efficiency: 0.0539, distance: 7.3134, memory: 0.1685, power: 0.2343
Test Batch 1/7, reward: -63.396, revenue_rate: 0.1918, efficiency: 0.0412, distance: 6.4993, memory: 0.1008, power: 0.2190
Test Batch 2/7, reward: -98.886, revenue_rate: 0.2126, efficiency: 0.0498, distance: 7.2797, memory: 0.1247, power: 0.2320
Test Batch 3/7, reward: 2.584, revenue_rate: 0.1877, efficiency: 0.0375, distance: 5.7431, memory: 0.0278, power: 0.1964
Test Batch 4/7, reward: -84.762, revenue_rate: 0.1771, efficiency: 0.0336, distance: 5.4722, memory: 0.0714, power: 0.1809
Test Batch 5/7, reward: -100.534, revenue_rate: 0.1962, efficiency: 0.0422, distance: 6.3438, memory: 0.1524, power: 0.2064
Test Batch 6/7, reward: 17.458, revenue_rate: 0.2114, efficiency: 0.0476, distance: 7.1855, memory: 0.0673, power: 0.2210
Test Summary - Avg reward: -68.293±110.165, revenue_rate: 0.1975±0.0246, efficiency: 0.0432, completion_rate: 0.2175, distance: 6.4717, memory: 0.1060, power: 0.2119
Load Balance - Avg balance score: 0.6273±0.1521
Task Distribution by Satellite:
  Satellite 1: 930 tasks (21.85%)
  Satellite 2: 1583 tasks (37.19%)
  Satellite 3: 1743 tasks (40.95%)
测试完成 - 平均奖励: -68.293, 平均星座收益率: 0.1975
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_05_10_37_55
   平均奖励: -68.2928
   收益率: 0.1975

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 200
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 16
seed: 12346
train-size: 1000
valid-size: 100
epochs: 2
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_05_10_53_33
使用模型: gpn_transformer
Actor参数数量: 5,929,732
Critic参数数量: 2,942,081

开始训练 Epoch 1/2
Batch 0: Reward: -129.6971, Loss: 20749.1836, Revenue: 0.2442, LoadBalance: 0.3684, Tasks: [S0:295(33.5%), S1:115(13.1%), S2:470(53.4%)], ActorGrad: 49318.2773, CriticGrad: 14096.5654, Advantage: μ=-130.303, σ=63.417, range=[-282.57, -43.57]
Batch 5: Reward: -1187.9296, Loss: 1484380.7500, Revenue: 0.6979, LoadBalance: 0.0000, Tasks: [S0:55(2.0%), S1:2733(97.1%), S2:28(1.0%)], ActorGrad: 31131.7969, CriticGrad: 23207.3457, Advantage: μ=-39.739, σ=10.000, range=[-60.38, -32.38]
Epoch 1, Batch 10/63, loss: 1981257.568↑, reward: -1277.789↓, critic_reward: -3.411, revenue_rate: 0.6543, distance: 24.9138, memory: 0.5073, power: 0.8320, lr: 0.000400, took: 111.969s
Batch 10: Reward: -875.5525, Loss: 761844.5000, Revenue: 0.5785, LoadBalance: 0.0000, Tasks: [S0:32(1.2%), S1:2368(91.9%), S2:176(6.8%)], ActorGrad: 651962.1250, CriticGrad: 13434.5684, Advantage: μ=-870.613, σ=64.305, range=[-968.09, -752.54]
Batch 15: Reward: -191.5765, Loss: 37037.0078, Revenue: 0.3118, LoadBalance: 0.1791, Tasks: [S0:377(30.6%), S1:95(7.7%), S2:760(61.7%)], ActorGrad: 49611.4570, CriticGrad: 2886.5134, Advantage: μ=-186.466, σ=49.181, range=[-323.99, -133.53]
Epoch 1, Batch 20/63, loss: 371430.045↓, reward: -515.453↑, critic_reward: -5.197, revenue_rate: 0.4939, distance: 18.6815, memory: 0.3476, power: 0.6230, lr: 0.000400, took: 75.578s
Batch 20: Reward: -122.2616, Loss: 14136.8164, Revenue: 0.2549, LoadBalance: 0.3132, Tasks: [S0:424(42.1%), S1:86(8.5%), S2:498(49.4%)], ActorGrad: 30420.4043, CriticGrad: 1690.7247, Advantage: μ=-116.786, σ=23.045, range=[-175.66, -85.94]
Batch 25: Reward: -86.1710, Loss: 6744.8818, Revenue: 0.2031, LoadBalance: 0.4890, Tasks: [S0:138(14.6%), S1:412(43.6%), S2:394(41.7%)], ActorGrad: 38862.2031, CriticGrad: 1185.1315, Advantage: μ=-80.530, σ=16.649, range=[-119.03, -58.96]
Epoch 1, Batch 30/63, loss: 19059.199↑, reward: -126.005↓, critic_reward: -5.624, revenue_rate: 0.2395, distance: 8.4394, memory: 0.2203, power: 0.3241, lr: 0.000400, took: 46.353s
Batch 30: Reward: -144.0360, Loss: 21513.0938, Revenue: 0.2368, LoadBalance: 0.2926, Tasks: [S0:120(11.2%), S1:610(56.9%), S2:342(31.9%)], ActorGrad: 86808.1484, CriticGrad: 1909.3661, Advantage: μ=-138.343, σ=50.324, range=[-266.72, -81.92]
Batch 35: Reward: -194.2109, Loss: 36965.4258, Revenue: 0.4322, LoadBalance: 0.1473, Tasks: [S0:80(4.1%), S1:1185(60.2%), S2:703(35.7%)], ActorGrad: 161708.1562, CriticGrad: 2730.5078, Advantage: μ=-188.123, σ=40.991, range=[-269.44, -121.67]
Epoch 1, Batch 40/63, loss: 85635.199↑, reward: -274.391↓, critic_reward: -5.944, revenue_rate: 0.3677, distance: 14.2599, memory: 0.3054, power: 0.5293, lr: 0.000400, took: 71.299s
Batch 40: Reward: -405.1927, Loss: 163103.7812, Revenue: 0.5899, LoadBalance: 0.0000, Tasks: [S0:44(1.5%), S1:2155(74.0%), S2:713(24.5%)], ActorGrad: 469791.8125, CriticGrad: 5632.8555, Advantage: μ=-399.150, σ=63.525, range=[-542.01, -314.04]
Batch 45: Reward: -589.4906, Loss: 463464.6250, Revenue: 0.6150, LoadBalance: 0.0031, Tasks: [S0:24(0.8%), S1:2127(72.2%), S2:793(26.9%)], ActorGrad: 26056.2207, CriticGrad: 7936.3999, Advantage: μ=-16.096, σ=10.000, range=[-32.03, -7.29]
Epoch 1, Batch 50/63, loss: 247894.740↓, reward: -431.768↑, critic_reward: -6.245, revenue_rate: 0.5620, distance: 22.7070, memory: 0.4899, power: 0.8548, lr: 0.000400, took: 106.510s
Batch 50: Reward: -163.4382, Loss: 24897.4844, Revenue: 0.5438, LoadBalance: 0.1800, Tasks: [S0:63(2.4%), S1:1138(43.9%), S2:1391(53.7%)], ActorGrad: 124961.2656, CriticGrad: 2136.0693, Advantage: μ=-157.245, σ=13.523, range=[-193.80, -142.58]
Batch 55: Reward: -132.0452, Loss: 17216.1523, Revenue: 0.2756, LoadBalance: 0.2924, Tasks: [S0:98(8.0%), S1:640(51.9%), S2:494(40.1%)], ActorGrad: 74953.1641, CriticGrad: 1769.9523, Advantage: μ=-125.445, σ=39.728, range=[-215.20, -83.93]
Epoch 1, Batch 60/63, loss: 20388.145↓, reward: -135.012↑, critic_reward: -6.462, revenue_rate: 0.3797, distance: 14.9260, memory: 0.3122, power: 0.5413, lr: 0.000400, took: 67.479s
Batch 60: Reward: -108.4825, Loss: 12884.7500, Revenue: 0.1969, LoadBalance: 0.4449, Tasks: [S0:502(54.1%), S1:198(21.3%), S2:228(24.6%)], ActorGrad: 59057.9570, CriticGrad: 1512.1736, Advantage: μ=-101.655, σ=52.165, range=[-230.41, -40.22]

📊 Epoch 1 训练统计:
  平均奖励: -441.9931
  平均损失: 432980.1487
  平均收益率: 0.4375
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: -84.943, revenue_rate: 0.1851, efficiency: 0.0379, distance: 6.1105, memory: 0.1260, power: 0.2002
Test Batch 1/7, reward: -98.679, revenue_rate: 0.2153, efficiency: 0.0492, distance: 6.6951, memory: 0.0878, power: 0.2219
Test Batch 2/7, reward: -125.364, revenue_rate: 0.2126, efficiency: 0.0500, distance: 7.1114, memory: 0.1324, power: 0.2291
Test Batch 3/7, reward: -75.672, revenue_rate: 0.2272, efficiency: 0.0567, distance: 7.6887, memory: 0.1921, power: 0.2400
Test Batch 4/7, reward: -75.451, revenue_rate: 0.1984, efficiency: 0.0446, distance: 6.8166, memory: 0.2113, power: 0.2131
Test Batch 5/7, reward: -99.233, revenue_rate: 0.1945, efficiency: 0.0398, distance: 5.9731, memory: 0.1022, power: 0.2050
Test Batch 6/7, reward: -53.493, revenue_rate: 0.1726, efficiency: 0.0319, distance: 5.8086, memory: 0.0894, power: 0.1653
Test Summary - Avg reward: -91.635±45.440, revenue_rate: 0.2042±0.0247, efficiency: 0.0458, completion_rate: 0.2230, distance: 6.6956, memory: 0.1399, power: 0.2161
Load Balance - Avg balance score: 0.5143±0.1658
Task Distribution by Satellite:
  Satellite 1: 1255 tasks (28.73%)
  Satellite 2: 2180 tasks (49.91%)
  Satellite 3: 933 tasks (21.36%)
✅ 验证完成 - Epoch 1, reward: -91.635, revenue_rate: 0.2042, distance: 6.6956, memory: 0.1399, power: 0.2161
  ⚠️ 欠拟合: 训练验证差距 = -350.3586
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_competitive_2025_08_05_10_53_33 (验证集奖励: -91.6345)

开始训练 Epoch 2/2
Batch 0: Reward: -85.9481, Loss: 7405.4409, Revenue: 0.1659, LoadBalance: 0.5244, Tasks: [S0:209(25.6%), S1:413(50.6%), S2:194(23.8%)], ActorGrad: 52033.0938, CriticGrad: 1150.9050, Advantage: μ=-79.223, σ=34.706, range=[-128.84, -24.80]
Batch 5: Reward: -11.1628, Loss: 11533.1611, Revenue: 0.1935, LoadBalance: 0.7424, Tasks: [S0:275(30.7%), S1:314(35.0%), S2:307(34.3%)], ActorGrad: 6109.4624, CriticGrad: 399.8192, Advantage: μ=-0.393, σ=10.000, range=[-6.06, 25.90]
Epoch 2, Batch 10/63, loss: 11110.169↓, reward: -69.055↑, critic_reward: -6.782, revenue_rate: 0.1785, distance: 6.1205, memory: 0.1863, power: 0.2639, lr: 0.000400, took: 42.403s
Batch 10: Reward: 5.6899, Loss: 16503.4492, Revenue: 0.1517, LoadBalance: 0.7204, Tasks: [S0:261(36.2%), S1:277(38.5%), S2:182(25.3%)], ActorGrad: 4813.6045, CriticGrad: 306.5705, Advantage: μ=0.953, σ=10.000, range=[-5.26, 26.26]
Batch 15: Reward: -7.6305, Loss: 14259.9219, Revenue: 0.1686, LoadBalance: 0.7302, Tasks: [S0:316(40.3%), S1:190(24.2%), S2:278(35.5%)], ActorGrad: 4036.2463, CriticGrad: 321.7183, Advantage: μ=-0.061, σ=10.000, range=[-6.59, 25.35]
Epoch 2, Batch 20/63, loss: 13490.841↑, reward: -10.123↑, critic_reward: -6.730, revenue_rate: 0.1630, distance: 5.4795, memory: 0.1738, power: 0.2390, lr: 0.000400, took: 34.811s
Batch 20: Reward: 75.9797, Loss: 29594.0059, Revenue: 0.1922, LoadBalance: 0.8484, Tasks: [S0:272(31.5%), S1:297(34.4%), S2:295(34.1%)], ActorGrad: 7598.1392, CriticGrad: 1399.3252, Advantage: μ=5.303, σ=10.000, range=[-3.08, 19.13]
Batch 25: Reward: 47.0116, Loss: 22136.1270, Revenue: 0.1495, LoadBalance: 0.7852, Tasks: [S0:261(37.9%), S1:238(34.6%), S2:189(27.5%)], ActorGrad: 4151.0161, CriticGrad: 1083.3009, Advantage: μ=3.706, σ=10.000, range=[-4.21, 18.88]
Epoch 2, Batch 30/63, loss: 20538.786↓, reward: 31.322↓, critic_reward: -5.820, revenue_rate: 0.1656, distance: 5.6292, memory: 0.1473, power: 0.2327, lr: 0.000400, took: 32.573s
Batch 30: Reward: 59.2528, Loss: 25077.5723, Revenue: 0.1686, LoadBalance: 0.7783, Tasks: [S0:255(33.2%), S1:246(32.0%), S2:267(34.8%)], ActorGrad: 4678.4375, CriticGrad: 1510.3966, Advantage: μ=4.139, σ=10.000, range=[-6.08, 19.26]
Batch 35: Reward: 44.0450, Loss: 22552.3320, Revenue: 0.1819, LoadBalance: 0.7690, Tasks: [S0:209(27.2%), S1:269(35.0%), S2:290(37.8%)], ActorGrad: 4247.5122, CriticGrad: 1280.7399, Advantage: μ=2.943, σ=10.000, range=[-5.41, 19.28]
Epoch 2, Batch 40/63, loss: 15304.476↓, reward: -0.033↓, critic_reward: -0.062, revenue_rate: 0.1802, distance: 6.2347, memory: 0.1446, power: 0.2507, lr: 0.000400, took: 35.080s
Batch 40: Reward: 3.2992, Loss: 18282.7480, Revenue: 0.2163, LoadBalance: 0.7095, Tasks: [S0:352(36.7%), S1:367(38.2%), S2:241(25.1%)], ActorGrad: 4332.2549, CriticGrad: 365.5858, Advantage: μ=0.147, σ=10.000, range=[-5.52, 21.25]
Batch 45: Reward: -62.6523, Loss: 4147.8975, Revenue: 0.2311, LoadBalance: 0.6544, Tasks: [S0:261(25.5%), S1:295(28.8%), S2:468(45.7%)], ActorGrad: 35474.4336, CriticGrad: 1348.2858, Advantage: μ=-61.957, σ=18.163, range=[-91.87, -31.98]
Epoch 2, Batch 50/63, loss: 9417.422↓, reward: -61.980↓, critic_reward: -0.558, revenue_rate: 0.1966, distance: 6.7072, memory: 0.1943, power: 0.2802, lr: 0.000400, took: 38.184s
Batch 50: Reward: -110.8023, Loss: 13242.0361, Revenue: 0.1938, LoadBalance: 0.4193, Tasks: [S0:502(54.1%), S1:154(16.6%), S2:272(29.3%)], ActorGrad: 62702.3906, CriticGrad: 2139.8713, Advantage: μ=-107.946, σ=41.179, range=[-211.54, -58.67]
Batch 55: Reward: -117.5617, Loss: 16511.8457, Revenue: 0.1916, LoadBalance: 0.4293, Tasks: [S0:181(19.2%), S1:510(54.0%), S2:253(26.8%)], ActorGrad: 166777.5781, CriticGrad: 2071.9485, Advantage: μ=-113.536, σ=62.152, range=[-247.65, -49.76]
Epoch 2, Batch 60/63, loss: 13723.926↑, reward: -78.626↓, critic_reward: -3.894, revenue_rate: 0.2126, distance: 7.8121, memory: 0.2053, power: 0.3164, lr: 0.000400, took: 41.321s
Batch 60: Reward: -114.6970, Loss: 12283.8965, Revenue: 0.3639, LoadBalance: 0.3731, Tasks: [S0:168(9.9%), S1:810(47.8%), S2:718(42.3%)], ActorGrad: 86480.3984, CriticGrad: 1761.4967, Advantage: μ=-109.916, σ=14.689, range=[-136.62, -81.73]

📊 Epoch 2 训练统计:
  平均奖励: -35.0627
  平均损失: 13781.4713
  平均收益率: 0.1878
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: -182.606, revenue_rate: 0.3516, efficiency: 0.1389, distance: 12.5497, memory: 0.1623, power: 0.3885
Test Batch 1/7, reward: -169.935, revenue_rate: 0.3545, efficiency: 0.1486, distance: 13.5535, memory: 0.1876, power: 0.4298
Test Batch 2/7, reward: -175.184, revenue_rate: 0.4293, efficiency: 0.2399, distance: 17.3584, memory: 0.4033, power: 0.5477
Test Batch 3/7, reward: -179.696, revenue_rate: 0.2925, efficiency: 0.0920, distance: 10.1775, memory: 0.1653, power: 0.3097
Test Batch 4/7, reward: -161.990, revenue_rate: 0.3587, efficiency: 0.1574, distance: 14.0302, memory: 0.3649, power: 0.4342
Test Batch 5/7, reward: -163.058, revenue_rate: 0.3688, efficiency: 0.1689, distance: 14.6351, memory: 0.2780, power: 0.4539
Test Batch 6/7, reward: -130.891, revenue_rate: 0.2604, efficiency: 0.0755, distance: 9.0081, memory: 0.2197, power: 0.2804
Test Summary - Avg reward: -170.431±51.789, revenue_rate: 0.3553±0.0518, efficiency: 0.1543, completion_rate: 0.4250, distance: 13.5290, memory: 0.2586, power: 0.4214
Load Balance - Avg balance score: 0.2257±0.1140
Task Distribution by Satellite:
  Satellite 1: 642 tasks (7.62%)
  Satellite 2: 2870 tasks (34.09%)
  Satellite 3: 4908 tasks (58.29%)
✅ 验证完成 - Epoch 2, reward: -170.431, revenue_rate: 0.3553, distance: 13.5290, memory: 0.2586, power: 0.4214
  ⚠️ 过拟合: 训练验证差距 = 135.3681
训练完成

开始测试模型...
Test Batch 0/7, reward: -163.028, revenue_rate: 0.3533, efficiency: 0.1482, distance: 13.5513, memory: 0.2705, power: 0.3986
Test Batch 1/7, reward: -153.740, revenue_rate: 0.3357, efficiency: 0.1239, distance: 11.4193, memory: 0.1700, power: 0.3664
Test Batch 2/7, reward: -151.917, revenue_rate: 0.2761, efficiency: 0.0867, distance: 9.7567, memory: 0.2025, power: 0.3124
Test Batch 3/7, reward: -156.056, revenue_rate: 0.3350, efficiency: 0.1321, distance: 12.5238, memory: 0.2736, power: 0.4017
Test Batch 4/7, reward: -171.073, revenue_rate: 0.3672, efficiency: 0.1593, distance: 13.8528, memory: 0.3410, power: 0.4278
Test Batch 5/7, reward: -148.985, revenue_rate: 0.3936, efficiency: 0.1828, distance: 14.1164, memory: 0.3470, power: 0.4693
Test Batch 6/7, reward: -128.922, revenue_rate: 0.2051, efficiency: 0.0451, distance: 6.7000, memory: 0.0682, power: 0.2196
Test Summary - Avg reward: -156.325±40.053, revenue_rate: 0.3380±0.0521, efficiency: 0.1351, completion_rate: 0.3920, distance: 12.3032, memory: 0.2595, power: 0.3890
Load Balance - Avg balance score: 0.2462±0.1004
Task Distribution by Satellite:
  Satellite 1: 598 tasks (7.71%)
  Satellite 2: 2751 tasks (35.47%)
  Satellite 3: 4407 tasks (56.82%)
测试完成 - 平均奖励: -156.325, 平均星座收益率: 0.3380
✅ 模式 COMPETITIVE 训练完成
   保存路径: constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_competitive_2025_08_05_10_53_33
   平均奖励: -156.3246
   收益率: 0.3380

🚀 [3/3] 开始训练模式: HYBRID

============================================================
开始训练星座模式: HYBRID
============================================================
constellation_smp: 200
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 16
seed: 12346
train-size: 1000
valid-size: 100
epochs: 2
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: hybrid
verbose: True
2025_08_05_11_07_56
使用模型: gpn_transformer
Actor参数数量: 5,929,732
Critic参数数量: 2,942,081

开始训练 Epoch 1/2
Batch 0: Reward: 28.6607, Loss: 57035.0273, Revenue: 0.1743, LoadBalance: 0.6892, Tasks: [S0:221(31.4%), S1:224(31.8%), S2:259(36.8%)], ActorGrad: 5460.7427, CriticGrad: 5301.5767, Advantage: μ=1.176, σ=10.000, range=[-7.76, 18.85]
Batch 5: Reward: -319.9022, Loss: 106993.1719, Revenue: 0.3907, LoadBalance: 0.1286, Tasks: [S0:55(3.3%), S1:1006(60.5%), S2:603(36.2%)], ActorGrad: 150023.0781, CriticGrad: 8002.2324, Advantage: μ=-317.663, σ=80.554, range=[-458.35, -212.90]
Epoch 1, Batch 10/63, loss: 671319.385↑, reward: -632.190↓, critic_reward: -1.635, revenue_rate: 0.4047, distance: 15.5047, memory: 0.3592, power: 0.5411, lr: 0.000400, took: 60.532s
Batch 10: Reward: -796.7142, Loss: 700762.0000, Revenue: 0.5610, LoadBalance: 0.0000, Tasks: [S0:30(1.2%), S1:560(23.0%), S2:1842(75.7%)], ActorGrad: 18490.8711, CriticGrad: 13452.2705, Advantage: μ=-28.471, σ=10.000, range=[-63.12, -19.21]
Batch 15: Reward: -1127.6917, Loss: 1277512.5000, Revenue: 0.3632, LoadBalance: 0.0000, Tasks: [S0:127(8.3%), S1:1317(85.7%), S2:92(6.0%)], ActorGrad: 35628.7656, CriticGrad: 16286.2002, Advantage: μ=-84.793, σ=10.000, range=[-99.83, -65.36]
Epoch 1, Batch 20/63, loss: 740795.973↓, reward: -760.567↑, critic_reward: -4.655, revenue_rate: 0.4047, distance: 15.3856, memory: 0.3114, power: 0.5376, lr: 0.000400, took: 55.969s
Batch 20: Reward: -168.9239, Loss: 27528.6875, Revenue: 0.2996, LoadBalance: 0.3946, Tasks: [S0:573(47.1%), S1:130(10.7%), S2:513(42.2%)], ActorGrad: 60673.4141, CriticGrad: 2287.5237, Advantage: μ=-163.884, σ=26.749, range=[-239.08, -120.26]
Batch 25: Reward: -79.2539, Loss: 20704.1230, Revenue: 0.1896, LoadBalance: 0.6207, Tasks: [S0:200(25.5%), S1:350(44.6%), S2:234(29.8%)], ActorGrad: 3358.6008, CriticGrad: 1119.5785, Advantage: μ=-5.786, σ=10.000, range=[-12.88, 30.51]
Epoch 1, Batch 30/63, loss: 95065.024↑, reward: -268.316↓, critic_reward: -5.309, revenue_rate: 0.3030, distance: 10.9934, memory: 0.2275, power: 0.4086, lr: 0.000400, took: 42.399s
Batch 30: Reward: -561.2421, Loss: 331105.5625, Revenue: 0.3822, LoadBalance: 0.0265, Tasks: [S0:1205(71.0%), S1:52(3.1%), S2:439(25.9%)], ActorGrad: 19148.6914, CriticGrad: 7753.9980, Advantage: μ=-36.208, σ=10.000, range=[-54.09, -22.18]
Batch 35: Reward: -378.9294, Loss: 176219.7500, Revenue: 0.2159, LoadBalance: 0.1981, Tasks: [S0:174(18.4%), S1:157(16.6%), S2:613(64.9%)], ActorGrad: 6649.9912, CriticGrad: 5266.9966, Advantage: μ=-18.813, σ=10.000, range=[-41.72, -9.65]
Epoch 1, Batch 40/63, loss: 130550.474↓, reward: -313.843↑, critic_reward: -5.620, revenue_rate: 0.2720, distance: 9.8256, memory: 0.2349, power: 0.3691, lr: 0.000400, took: 38.595s
Batch 40: Reward: -510.9694, Loss: 292522.5000, Revenue: 0.2598, LoadBalance: 0.0852, Tasks: [S0:794(69.9%), S1:224(19.7%), S2:118(10.4%)], ActorGrad: 11192.3311, CriticGrad: 7218.1914, Advantage: μ=-25.304, σ=10.000, range=[-46.16, -13.23]
Batch 45: Reward: 33.5201, Loss: 46363.7188, Revenue: 0.1691, LoadBalance: 0.7230, Tasks: [S0:260(36.1%), S1:281(39.0%), S2:179(24.9%)], ActorGrad: 2527.2395, CriticGrad: 911.5188, Advantage: μ=1.810, σ=10.000, range=[-6.00, 18.57]
Epoch 1, Batch 50/63, loss: 196442.925↓, reward: -336.091↑, critic_reward: -5.971, revenue_rate: 0.2353, distance: 8.1752, memory: 0.1768, power: 0.3207, lr: 0.000400, took: 34.182s
Batch 50: Reward: -129.8360, Loss: 17813.7910, Revenue: 0.2603, LoadBalance: 0.5681, Tasks: [S0:374(33.4%), S1:224(20.0%), S2:522(46.6%)], ActorGrad: 44703.7422, CriticGrad: 1677.3011, Advantage: μ=-123.955, σ=51.109, range=[-211.55, -57.25]
Batch 55: Reward: -118.4275, Loss: 40774.5195, Revenue: 0.1796, LoadBalance: 0.5378, Tasks: [S0:176(23.4%), S1:375(49.9%), S2:201(26.7%)], ActorGrad: 3241.4900, CriticGrad: 1673.6259, Advantage: μ=-6.476, σ=10.000, range=[-21.09, 25.61]
Epoch 1, Batch 60/63, loss: 39580.453↑, reward: -57.683↑, critic_reward: -6.189, revenue_rate: 0.1757, distance: 6.0426, memory: 0.1556, power: 0.2412, lr: 0.000400, took: 27.309s
Batch 60: Reward: -19.8440, Loss: 37507.0664, Revenue: 0.1670, LoadBalance: 0.7002, Tasks: [S0:295(40.1%), S1:269(36.5%), S2:172(23.4%)], ActorGrad: 2334.4080, CriticGrad: 583.0437, Advantage: μ=-0.686, σ=10.000, range=[-8.35, 26.67]

📊 Epoch 1 训练统计:
  平均奖励: -377.4533
  平均损失: 298696.3870
  平均收益率: 0.2925
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: -64.545, revenue_rate: 0.1460, efficiency: 0.0234, distance: 5.1788, memory: 0.1052, power: 0.1554
Test Batch 1/7, reward: 36.065, revenue_rate: 0.1524, efficiency: 0.0251, distance: 5.2401, memory: 0.1606, power: 0.1635
Test Batch 2/7, reward: 142.287, revenue_rate: 0.1839, efficiency: 0.0368, distance: 5.9491, memory: 0.0820, power: 0.1884
Test Batch 3/7, reward: -4.898, revenue_rate: 0.1844, efficiency: 0.0369, distance: 5.9655, memory: 0.1298, power: 0.1937
Test Batch 4/7, reward: 55.038, revenue_rate: 0.1716, efficiency: 0.0343, distance: 6.2104, memory: 0.1632, power: 0.1952
Test Batch 5/7, reward: 65.016, revenue_rate: 0.1652, efficiency: 0.0305, distance: 5.4524, memory: 0.1260, power: 0.1799
Test Batch 6/7, reward: 65.275, revenue_rate: 0.1481, efficiency: 0.0252, distance: 5.7365, memory: 0.1507, power: 0.1516
Test Summary - Avg reward: 39.245±242.597, revenue_rate: 0.1665±0.0236, efficiency: 0.0309, completion_rate: 0.1843, distance: 5.6689, memory: 0.1287, power: 0.1782
Load Balance - Avg balance score: 0.7480±0.1560
Task Distribution by Satellite:
  Satellite 1: 1258 tasks (35.06%)
  Satellite 2: 1186 tasks (33.05%)
  Satellite 3: 1144 tasks (31.88%)
✅ 验证完成 - Epoch 1, reward: 39.245, revenue_rate: 0.1665, distance: 5.6689, memory: 0.1287, power: 0.1782
  ⚠️ 欠拟合: 训练验证差距 = -416.6984
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_hybrid_2025_08_05_11_07_56 (验证集奖励: 39.2452)

开始训练 Epoch 2/2
Batch 0: Reward: 65.6681, Loss: 64468.8359, Revenue: 0.1587, LoadBalance: 0.7854, Tasks: [S0:244(33.2%), S1:258(35.1%), S2:234(31.8%)], ActorGrad: 2429.0332, CriticGrad: 1211.4965, Advantage: μ=2.852, σ=10.000, range=[-4.72, 21.36]
Batch 5: Reward: 31.7070, Loss: 57837.6562, Revenue: 0.1424, LoadBalance: 0.6876, Tasks: [S0:212(31.5%), S1:279(41.5%), S2:181(26.9%)], ActorGrad: 2884.6934, CriticGrad: 923.4034, Advantage: μ=1.542, σ=10.000, range=[-6.18, 20.30]
Epoch 2, Batch 10/63, loss: 45224.715↓, reward: 19.217↓, critic_reward: -6.082, revenue_rate: 0.1555, distance: 5.2247, memory: 0.1488, power: 0.2256, lr: 0.000400, took: 29.473s
Batch 10: Reward: 7.1444, Loss: 40773.5664, Revenue: 0.1315, LoadBalance: 0.7735, Tasks: [S0:189(30.3%), S1:214(34.3%), S2:221(35.4%)], ActorGrad: 2296.6021, CriticGrad: 525.0374, Advantage: μ=0.616, σ=10.000, range=[-4.82, 28.68]
Batch 15: Reward: 41.4132, Loss: 55963.7656, Revenue: 0.1428, LoadBalance: 0.7214, Tasks: [S0:250(37.2%), S1:201(29.9%), S2:221(32.9%)], ActorGrad: 2463.2878, CriticGrad: 895.4376, Advantage: μ=1.935, σ=10.000, range=[-5.54, 20.65]
Epoch 2, Batch 20/63, loss: 59050.004↑, reward: 54.615↑, critic_reward: -4.843, revenue_rate: 0.1576, distance: 5.2172, memory: 0.1594, power: 0.2246, lr: 0.000400, took: 25.341s
Batch 20: Reward: -43.8821, Loss: 23821.9883, Revenue: 0.2113, LoadBalance: 0.7441, Tasks: [S0:367(38.2%), S1:286(29.8%), S2:307(32.0%)], ActorGrad: 3122.4238, CriticGrad: 973.1633, Advantage: μ=-2.651, σ=10.000, range=[-12.95, 33.69]
Batch 25: Reward: 21.9889, Loss: 44650.9297, Revenue: 0.1942, LoadBalance: 0.7657, Tasks: [S0:319(36.9%), S1:297(34.4%), S2:248(28.7%)], ActorGrad: 2674.3752, CriticGrad: 820.0961, Advantage: μ=1.096, σ=10.000, range=[-5.45, 23.52]
Epoch 2, Batch 30/63, loss: 40409.601↓, reward: 6.443↓, critic_reward: -1.591, revenue_rate: 0.1713, distance: 5.8187, memory: 0.1732, power: 0.2435, lr: 0.000400, took: 27.157s
Batch 30: Reward: -100.4249, Loss: 11643.3145, Revenue: 0.1799, LoadBalance: 0.6494, Tasks: [S0:232(29.0%), S1:200(25.0%), S2:368(46.0%)], ActorGrad: 25329.3867, CriticGrad: 2314.6450, Advantage: μ=-100.478, σ=40.629, range=[-181.34, -47.49]
Batch 35: Reward: -44.4460, Loss: 17507.5957, Revenue: 0.1656, LoadBalance: 0.7368, Tasks: [S0:236(33.5%), S1:198(28.1%), S2:270(38.4%)], ActorGrad: 1788.2576, CriticGrad: 885.8731, Advantage: μ=-3.387, σ=10.000, range=[-9.48, 33.44]
Epoch 2, Batch 40/63, loss: 49607.987↑, reward: 32.058↑, critic_reward: -0.633, revenue_rate: 0.1721, distance: 5.8197, memory: 0.1471, power: 0.2363, lr: 0.000400, took: 26.132s
Batch 40: Reward: 13.4188, Loss: 45434.8906, Revenue: 0.1937, LoadBalance: 0.7300, Tasks: [S0:230(27.6%), S1:346(41.6%), S2:256(30.8%)], ActorGrad: 2063.5303, CriticGrad: 526.6131, Advantage: μ=0.628, σ=10.000, range=[-6.02, 20.66]
Batch 45: Reward: -20.7782, Loss: 43902.6836, Revenue: 0.1450, LoadBalance: 0.6736, Tasks: [S0:261(37.9%), S1:252(36.6%), S2:175(25.4%)], ActorGrad: 2825.2024, CriticGrad: 709.7164, Advantage: μ=-0.993, σ=10.000, range=[-7.22, 24.44]
Epoch 2, Batch 50/63, loss: 43431.365↓, reward: -2.643↓, critic_reward: 0.367, revenue_rate: 0.1686, distance: 5.6936, memory: 0.1612, power: 0.2329, lr: 0.000400, took: 26.290s
Batch 50: Reward: -115.7661, Loss: 14344.7432, Revenue: 0.1678, LoadBalance: 0.5695, Tasks: [S0:340(44.3%), S1:282(36.7%), S2:146(19.0%)], ActorGrad: 39968.6445, CriticGrad: 2372.4282, Advantage: μ=-117.049, σ=26.215, range=[-156.96, -68.23]
Batch 55: Reward: -151.9550, Loss: 24125.7559, Revenue: 0.1789, LoadBalance: 0.4435, Tasks: [S0:383(46.0%), S1:120(14.4%), S2:329(39.5%)], ActorGrad: 55789.3672, CriticGrad: 2924.1758, Advantage: μ=-152.534, σ=30.270, range=[-209.26, -90.18]
Epoch 2, Batch 60/63, loss: 29051.642↑, reward: -108.231↑, critic_reward: 0.338, revenue_rate: 0.1951, distance: 6.8313, memory: 0.1946, power: 0.2767, lr: 0.000400, took: 32.275s
Batch 60: Reward: 11.4361, Loss: 41925.0664, Revenue: 0.1543, LoadBalance: 0.7252, Tasks: [S0:274(38.1%), S1:181(25.1%), S2:265(36.8%)], ActorGrad: 2387.6741, CriticGrad: 477.6834, Advantage: μ=0.584, σ=10.000, range=[-5.84, 23.58]

📊 Epoch 2 训练统计:
  平均奖励: -3.1376
  平均损失: 43905.5055
  平均收益率: 0.1689
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: -74.630, revenue_rate: 0.1547, efficiency: 0.0263, distance: 5.2159, memory: 0.0876, power: 0.1761
Test Batch 1/7, reward: -16.698, revenue_rate: 0.1775, efficiency: 0.0336, distance: 5.8517, memory: 0.1277, power: 0.1843
Test Batch 2/7, reward: -72.376, revenue_rate: 0.1807, efficiency: 0.0361, distance: 6.1865, memory: 0.1416, power: 0.1930
Test Batch 3/7, reward: -72.221, revenue_rate: 0.1838, efficiency: 0.0367, distance: 6.0169, memory: 0.1413, power: 0.1965
Test Batch 4/7, reward: -45.721, revenue_rate: 0.1939, efficiency: 0.0445, distance: 7.0191, memory: 0.1738, power: 0.2245
Test Batch 5/7, reward: 5.626, revenue_rate: 0.1890, efficiency: 0.0386, distance: 5.7138, memory: 0.1260, power: 0.2030
Test Batch 6/7, reward: -100.428, revenue_rate: 0.1489, efficiency: 0.0246, distance: 5.7101, memory: 0.1621, power: 0.1405
Test Summary - Avg reward: -48.180±176.273, revenue_rate: 0.1787±0.0242, efficiency: 0.0355, completion_rate: 0.1974, distance: 5.9890, memory: 0.1342, power: 0.1940
Load Balance - Avg balance score: 0.6465±0.1543
Task Distribution by Satellite:
  Satellite 1: 1282 tasks (33.25%)
  Satellite 2: 1675 tasks (43.44%)
  Satellite 3: 899 tasks (23.31%)
✅ 验证完成 - Epoch 2, reward: -48.180, revenue_rate: 0.1787, distance: 5.9890, memory: 0.1342, power: 0.1940
  ⚠️ 过拟合: 训练验证差距 = 45.0427
训练完成

开始测试模型...
Test Batch 0/7, reward: -106.767, revenue_rate: 0.1761, efficiency: 0.0361, distance: 6.2762, memory: 0.1862, power: 0.1894
Test Batch 1/7, reward: -93.474, revenue_rate: 0.1643, efficiency: 0.0304, distance: 5.4319, memory: 0.0879, power: 0.1838
Test Batch 2/7, reward: -120.721, revenue_rate: 0.2220, efficiency: 0.0520, distance: 7.1578, memory: 0.1140, power: 0.2261
Test Batch 3/7, reward: -132.696, revenue_rate: 0.1935, efficiency: 0.0406, distance: 6.5756, memory: 0.1011, power: 0.2126
Test Batch 4/7, reward: -107.340, revenue_rate: 0.1940, efficiency: 0.0407, distance: 6.1898, memory: 0.0887, power: 0.2023
Test Batch 5/7, reward: -129.237, revenue_rate: 0.2016, efficiency: 0.0443, distance: 6.4143, memory: 0.1013, power: 0.2173
Test Batch 6/7, reward: -127.144, revenue_rate: 0.1951, efficiency: 0.0419, distance: 5.9728, memory: 0.1300, power: 0.2074
Test Summary - Avg reward: -115.523±79.290, revenue_rate: 0.1921±0.0269, efficiency: 0.0407, completion_rate: 0.2107, distance: 6.3262, memory: 0.1139, power: 0.2053
Load Balance - Avg balance score: 0.5856±0.1588
Task Distribution by Satellite:
  Satellite 1: 1329 tasks (32.26%)
  Satellite 2: 1897 tasks (46.04%)
  Satellite 3: 894 tasks (21.70%)
测试完成 - 平均奖励: -115.523, 平均星座收益率: 0.1921
✅ 模式 HYBRID 训练完成
   保存路径: constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_hybrid_2025_08_05_11_07_56
   平均奖励: -115.5235
   收益率: 0.1921

================================================================================
🎯 多模式训练总结
================================================================================
✅ COOPERATIVE: 奖励=-68.2928, 收益率=0.1975
✅ COMPETITIVE: 奖励=-156.3246, 收益率=0.3380
✅ HYBRID: 奖励=-115.5235, 收益率=0.1921

🏆 最佳模式: COOPERATIVE
   最高奖励: -68.2928
   对应收益率: 0.1975
   模型路径: constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_05_10_37_55

🎉 所有模式训练完成！
