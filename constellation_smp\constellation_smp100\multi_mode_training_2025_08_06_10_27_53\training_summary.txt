🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_06_10_27_53
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_06_10_27_57
使用模型: gpn_transformer
Actor参数数量: 4,383,364
Critic参数数量: 2,152,321

开始训练 Epoch 1/3
❌ 模式 COOPERATIVE 训练失败: view size is not compatible with input tensor's size and stride (at least one dimension spans across two contiguous subspaces). Use .reshape(...) instead.
错误详情:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\0801single_smp\train_constellation.py", line 354, in train_constellation_smp
    save_dir, avg_reward, revenue_rate = train_single_constellation_mode(mode)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\0801single_smp\train_constellation.py", line 271, in train_single_constellation_mode
    train_constellation_smp_process(actor, critic, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\0801single_smp\train_constellation.py", line 472, in train_constellation_smp_process
    tour_indices, satellite_indices, tour_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\0801single_smp\constellation_smp\gpn_transformer.py", line 161, in forward
    constellation_features, satellite_features = self.transformer_encoder(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\0801single_smp\constellation_smp\transformer_encoder.py", line 249, in forward
    x = combined_features.view(-1, input_size)
RuntimeError: view size is not compatible with input tensor's size and stride (at least one dimension spans across two contiguous subspaces). Use .reshape(...) instead.


🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
