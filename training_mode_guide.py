#!/usr/bin/env python3
"""
星座任务规划训练模式使用指南
"""

def print_usage_guide():
    """打印使用指南"""
    print("🛰️  星座任务规划训练模式选择指南")
    print("=" * 60)
    
    print("\n📋 可用的训练模式:")
    print("   1. single   - 单一模式训练")
    print("   2. all      - 所有模式训练 (cooperative, competitive, hybrid)")
    print("   3. compare  - 自定义对比训练")
    
    print("\n🚀 使用示例:")
    
    print("\n1️⃣  单一模式训练 (默认competitive):")
    print("   python train_constellation.py --training_mode single")
    print("   python train_constellation.py --training_mode single --constellation_mode cooperative")
    
    print("\n2️⃣  训练所有三种模式:")
    print("   python train_constellation.py --training_mode all")
    
    print("\n3️⃣  自定义对比训练:")
    print("   python train_constellation.py --training_mode compare --modes_to_train cooperative competitive")
    print("   python train_constellation.py --training_mode compare --modes_to_train hybrid")
    
    print("\n📊 输出结果:")
    print("   • 单模式: 显示该模式的训练结果")
    print("   • 多模式: 显示所有模式的对比结果和最佳模式")
    
    print("\n📁 保存目录:")
    print("   每种模式会保存在独立的目录中:")
    print("   • constellation_gpn_transformer_cooperative_YYYY_MM_DD_HH_MM_SS")
    print("   • constellation_gpn_transformer_competitive_YYYY_MM_DD_HH_MM_SS")
    print("   • constellation_gpn_transformer_hybrid_YYYY_MM_DD_HH_MM_SS")
    
    print("\n⚙️  其他参数:")
    print("   所有原有参数仍然有效:")
    print("   --epochs 5          # 训练轮数")
    print("   --num_satellites 3  # 卫星数量")
    print("   --batch_size 128    # 批次大小")
    print("   --model gpn         # 模型类型")
    print("   --rnn transformer   # RNN类型")
    
    print("\n💡 推荐使用:")
    print("   • 快速测试: --training_mode single")
    print("   • 性能对比: --training_mode all")
    print("   • 特定对比: --training_mode compare --modes_to_train cooperative competitive")

def test_current_config():
    """测试当前配置"""
    try:
        from hyperparameter import args
        print(f"\n🔧 当前配置:")
        print(f"   训练模式: {args.training_mode}")
        print(f"   星座模式: {args.constellation_mode}")
        if hasattr(args, 'modes_to_train') and args.modes_to_train:
            print(f"   指定模式: {args.modes_to_train}")
        print(f"   模型类型: {args.model}")
        print(f"   RNN类型: {args.rnn}")
        print(f"   训练轮数: {args.epochs}")
        print(f"   卫星数量: {args.num_satellites}")
        
        # 预测将要训练的模式
        if args.training_mode == 'single':
            modes = [args.constellation_mode]
        elif args.training_mode == 'all':
            modes = ['cooperative', 'competitive', 'hybrid']
        elif args.training_mode == 'compare':
            modes = args.modes_to_train if args.modes_to_train else ['cooperative', 'competitive', 'hybrid']
        else:
            modes = [args.constellation_mode]
        
        print(f"\n📋 将要训练的模式: {', '.join(modes)}")
        print(f"   预计训练时间: ~{len(modes) * args.epochs * 80}秒")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")

def main():
    """主函数"""
    print_usage_guide()
    test_current_config()
    
    print("\n" + "=" * 60)
    print("🎯 准备开始训练？运行: python train_constellation.py")

if __name__ == "__main__":
    main()
