🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp200\multi_mode_training_2025_08_04_18_20_11
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 200
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 16
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_04_18_20_18
使用模型: gpn_transformer
Actor参数数量: 5,929,732
Critic参数数量: 2,942,081

开始训练 Epoch 1/3
Batch 0: Reward: 16.6328, Loss: 285.7186, Revenue: 0.1743, LoadBalance: 0.6892, Tasks: [S0:221(31.4%), S1:224(31.8%), S2:259(36.8%)], ActorGrad: 10542.7959, CriticGrad: 1489.0417, Advantage: μ=16.764, σ=2.235, range=[13.47, 21.29]
Batch 5: Reward: 25.9668, Loss: 432.2407, Revenue: 0.4306, LoadBalance: 0.0000, Tasks: [S0:118(6.5%), S1:109(6.0%), S2:1581(87.4%)], ActorGrad: 10799.8359, CriticGrad: 398.0681, Advantage: μ=20.711, σ=1.875, range=[17.82, 24.25]
Epoch 1, Batch 10/63, loss: 525.585↑, reward: 26.604↑, critic_reward: 4.540, revenue_rate: 0.4172, distance: 15.9090, memory: 0.3401, power: 0.5623, lr: 0.000400, took: 66.403s
Batch 10: Reward: 33.3126, Loss: 759.4451, Revenue: 0.6486, LoadBalance: 0.0000, Tasks: [S0:113(4.3%), S1:11(0.4%), S2:2484(95.2%)], ActorGrad: 24754.5723, CriticGrad: 428.2450, Advantage: μ=27.412, σ=2.925, range=[23.56, 34.26]
Batch 15: Reward: 30.5611, Loss: 591.3441, Revenue: 0.5022, LoadBalance: 0.0137, Tasks: [S0:74(3.3%), S1:552(24.6%), S2:1614(72.1%)], ActorGrad: 12970.5654, CriticGrad: 366.1909, Advantage: μ=24.265, σ=1.657, range=[21.28, 26.67]
Epoch 1, Batch 20/63, loss: 721.060↓, reward: 32.964↓, critic_reward: 6.454, revenue_rate: 0.5942, distance: 23.1534, memory: 0.4773, power: 0.7868, lr: 0.000400, took: 92.522s
Batch 20: Reward: 34.6786, Loss: 791.5670, Revenue: 0.6441, LoadBalance: 0.0000, Tasks: [S0:14(0.5%), S1:2532(94.8%), S2:126(4.7%)], ActorGrad: 17237.9121, CriticGrad: 423.5484, Advantage: μ=27.944, σ=3.376, range=[21.12, 33.99]
Batch 25: Reward: 39.5859, Loss: 1083.0992, Revenue: 0.6818, LoadBalance: 0.0000, Tasks: [S0:29(0.9%), S1:2934(95.0%), S2:125(4.0%)], ActorGrad: 19382.0312, CriticGrad: 476.4157, Advantage: μ=32.779, σ=3.032, range=[26.88, 36.92]
Epoch 1, Batch 30/63, loss: 726.356↓, reward: 33.367↓, critic_reward: 6.879, revenue_rate: 0.5893, distance: 22.7569, memory: 0.4414, power: 0.7912, lr: 0.000400, took: 87.511s
Batch 30: Reward: 24.7962, Loss: 320.4839, Revenue: 0.3694, LoadBalance: 0.3199, Tasks: [S0:152(9.6%), S1:847(53.5%), S2:585(36.9%)], ActorGrad: 6345.4507, CriticGrad: 256.5855, Advantage: μ=17.687, σ=2.859, range=[13.73, 23.06]
Batch 35: Reward: 36.3162, Loss: 851.5576, Revenue: 0.6109, LoadBalance: 0.0000, Tasks: [S0:67(2.4%), S1:166(5.8%), S2:2615(91.8%)], ActorGrad: 15191.8477, CriticGrad: 419.9457, Advantage: μ=29.036, σ=3.005, range=[23.38, 34.61]
Epoch 1, Batch 40/63, loss: 576.254↑, reward: 30.574↑, critic_reward: 7.175, revenue_rate: 0.5028, distance: 20.2946, memory: 0.4522, power: 0.7111, lr: 0.000400, took: 80.032s
Batch 40: Reward: 37.2718, Loss: 902.5234, Revenue: 0.6178, LoadBalance: 0.0000, Tasks: [S0:20(0.7%), S1:226(8.2%), S2:2522(91.1%)], ActorGrad: 18725.1914, CriticGrad: 431.2024, Advantage: μ=29.910, σ=2.908, range=[25.90, 37.85]
Batch 45: Reward: 36.1377, Loss: 833.9863, Revenue: 0.6049, LoadBalance: 0.0000, Tasks: [S0:19(0.7%), S1:470(17.7%), S2:2167(81.6%)], ActorGrad: 20604.4160, CriticGrad: 404.2685, Advantage: μ=28.725, σ=3.075, range=[21.87, 33.50]
Epoch 1, Batch 50/63, loss: 885.944↓, reward: 37.121↓, critic_reward: 7.514, revenue_rate: 0.6206, distance: 23.4845, memory: 0.4687, power: 0.8615, lr: 0.000400, took: 95.426s
Batch 50: Reward: 34.4455, Loss: 729.5616, Revenue: 0.5706, LoadBalance: 0.0000, Tasks: [S0:19(0.7%), S1:558(21.7%), S2:1999(77.6%)], ActorGrad: 18970.6406, CriticGrad: 380.4363, Advantage: μ=26.912, σ=2.377, range=[21.46, 32.02]
Batch 55: Reward: 34.7331, Loss: 736.9343, Revenue: 0.5222, LoadBalance: 0.0661, Tasks: [S0:30(1.3%), S1:813(35.5%), S2:1445(63.2%)], ActorGrad: 17702.4961, CriticGrad: 387.4164, Advantage: μ=27.050, σ=2.366, range=[23.24, 31.43]
Epoch 1, Batch 60/63, loss: 848.647↑, reward: 36.732↑, critic_reward: 7.752, revenue_rate: 0.5742, distance: 21.7892, memory: 0.4665, power: 0.8088, lr: 0.000400, took: 91.370s
Batch 60: Reward: 37.3009, Loss: 885.0013, Revenue: 0.5574, LoadBalance: 0.0944, Tasks: [S0:49(1.9%), S1:1586(61.6%), S2:941(36.5%)], ActorGrad: 18642.4863, CriticGrad: 415.3367, Advantage: μ=29.573, σ=3.337, range=[24.99, 36.57]

📊 Epoch 1 训练统计:
  平均奖励: 32.9717
  平均损失: 714.4338
  平均收益率: 0.5480
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 24.669, revenue_rate: 0.4849, efficiency: 0.2904, distance: 19.3034, memory: 0.4637, power: 0.5792
Test Batch 1/7, reward: 24.469, revenue_rate: 0.4604, efficiency: 0.2620, distance: 17.9680, memory: 0.3356, power: 0.5911
Test Batch 2/7, reward: 23.331, revenue_rate: 0.4453, efficiency: 0.2357, distance: 16.5448, memory: 0.3000, power: 0.5228
Test Batch 3/7, reward: 27.886, revenue_rate: 0.5011, efficiency: 0.3251, distance: 20.0714, memory: 0.3716, power: 0.6388
Test Batch 4/7, reward: 26.232, revenue_rate: 0.5106, efficiency: 0.3309, distance: 21.0521, memory: 0.3996, power: 0.6277
Test Batch 5/7, reward: 25.418, revenue_rate: 0.5088, efficiency: 0.3173, distance: 20.2026, memory: 0.4212, power: 0.6266
Test Batch 6/7, reward: 21.530, revenue_rate: 0.4335, efficiency: 0.2254, distance: 17.5895, memory: 0.2631, power: 0.5064
Test Summary - Avg reward: 25.182±2.562, revenue_rate: 0.4831±0.0552, efficiency: 0.2908, completion_rate: 0.5996, distance: 19.1264, memory: 0.3772, power: 0.5941
Load Balance - Avg balance score: -0.3084±0.0795
Task Distribution by Satellite:
  Satellite 1: 373 tasks (3.13%)
  Satellite 2: 9928 tasks (83.32%)
  Satellite 3: 1615 tasks (13.55%)
✅ 验证完成 - Epoch 1, reward: 25.182, revenue_rate: 0.4831, distance: 19.1264, memory: 0.3772, power: 0.5941
  ⚠️ 过拟合: 训练验证差距 = 7.7897
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_04_18_20_18 (验证集奖励: 25.1819)

开始训练 Epoch 2/3
Batch 0: Reward: 29.0110, Loss: 460.8579, Revenue: 0.4408, LoadBalance: 0.0000, Tasks: [S0:66(3.3%), S1:1561(78.7%), S2:357(18.0%)], ActorGrad: 11321.1084, CriticGrad: 293.1382, Advantage: μ=21.305, σ=2.721, range=[17.38, 25.57]
Batch 5: Reward: 30.6950, Loss: 514.8998, Revenue: 0.4969, LoadBalance: 0.0000, Tasks: [S0:117(4.9%), S1:2200(92.3%), S2:67(2.8%)], ActorGrad: 12263.6621, CriticGrad: 326.5696, Advantage: μ=22.571, σ=2.414, range=[17.82, 25.64]
Epoch 2, Batch 10/63, loss: 529.250↑, reward: 30.578↑, critic_reward: 8.033, revenue_rate: 0.4778, distance: 18.8586, memory: 0.4440, power: 0.7149, lr: 0.000400, took: 82.436s
Batch 10: Reward: 36.1900, Loss: 779.6920, Revenue: 0.5629, LoadBalance: 0.0000, Tasks: [S0:105(3.8%), S1:2634(95.2%), S2:29(1.0%)], ActorGrad: 16747.7168, CriticGrad: 408.0090, Advantage: μ=27.811, σ=2.576, range=[23.16, 32.35]
Batch 15: Reward: 40.7162, Loss: 1073.0442, Revenue: 0.6469, LoadBalance: 0.0000, Tasks: [S0:93(2.9%), S1:3081(96.8%), S2:10(0.3%)], ActorGrad: 21781.3398, CriticGrad: 461.6171, Advantage: μ=32.601, σ=3.298, range=[27.47, 38.90]
Epoch 2, Batch 20/63, loss: 978.492↑, reward: 39.448↑, critic_reward: 8.332, revenue_rate: 0.6277, distance: 25.4777, memory: 0.5721, power: 0.9460, lr: 0.000400, took: 116.296s
Batch 20: Reward: 41.3442, Loss: 1088.1118, Revenue: 0.6684, LoadBalance: 0.0000, Tasks: [S0:130(4.1%), S1:3046(95.7%), S2:8(0.3%)], ActorGrad: 26887.6660, CriticGrad: 480.0833, Advantage: μ=32.884, σ=2.681, range=[28.74, 37.51]
Batch 25: Reward: 39.7665, Loss: 989.6056, Revenue: 0.6110, LoadBalance: 0.0000, Tasks: [S0:180(6.0%), S1:2838(93.8%), S2:6(0.2%)], ActorGrad: 20686.1973, CriticGrad: 451.5049, Advantage: μ=31.352, σ=2.659, range=[28.32, 39.39]
Epoch 2, Batch 30/63, loss: 977.716↓, reward: 39.698↓, critic_reward: 8.598, revenue_rate: 0.6297, distance: 24.9136, memory: 0.5247, power: 0.9514, lr: 0.000400, took: 114.924s
Batch 30: Reward: 40.4723, Loss: 1016.9680, Revenue: 0.6694, LoadBalance: 0.0000, Tasks: [S0:105(3.3%), S1:3073(96.5%), S2:6(0.2%)], ActorGrad: 25132.9199, CriticGrad: 470.0902, Advantage: μ=31.742, σ=3.173, range=[25.87, 38.59]
Batch 35: Reward: 37.9812, Loss: 862.8190, Revenue: 0.5766, LoadBalance: 0.0000, Tasks: [S0:146(5.0%), S1:2738(94.5%), S2:12(0.4%)], ActorGrad: 21574.7754, CriticGrad: 426.8468, Advantage: μ=29.217, σ=3.126, range=[21.27, 33.76]
Epoch 2, Batch 40/63, loss: 927.785↓, reward: 39.136↓, critic_reward: 8.869, revenue_rate: 0.6146, distance: 24.3487, memory: 0.5970, power: 0.9414, lr: 0.000400, took: 114.881s
Batch 40: Reward: 42.8473, Loss: 1161.7360, Revenue: 0.6601, LoadBalance: 0.0000, Tasks: [S0:74(2.3%), S1:3101(97.4%), S2:9(0.3%)], ActorGrad: 33711.1172, CriticGrad: 502.0459, Advantage: μ=33.944, σ=3.186, range=[29.69, 41.38]
Batch 45: Reward: 41.4865, Loss: 1092.7654, Revenue: 0.7047, LoadBalance: 0.0000, Tasks: [S0:9(0.3%), S1:3170(99.6%), S2:5(0.2%)], ActorGrad: 37936.9062, CriticGrad: 465.1397, Advantage: μ=32.934, σ=2.945, range=[27.95, 38.00]
Epoch 2, Batch 50/63, loss: 1107.004↓, reward: 42.088↓, critic_reward: 8.940, revenue_rate: 0.6889, distance: 24.2337, memory: 0.5916, power: 0.9920, lr: 0.000400, took: 123.459s
Batch 50: Reward: 41.9228, Loss: 1086.1278, Revenue: 0.7217, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:3175(99.7%), S2:6(0.2%)], ActorGrad: 40575.8281, CriticGrad: 482.1229, Advantage: μ=32.917, σ=1.675, range=[30.92, 37.47]
Batch 55: Reward: 42.8095, Loss: 1123.6697, Revenue: 0.7161, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:3181(99.9%), S2:2(0.1%)], ActorGrad: 40114.0430, CriticGrad: 501.0407, Advantage: μ=33.467, σ=1.973, range=[29.23, 36.52]
Epoch 2, Batch 60/63, loss: 1077.739↓, reward: 41.941↓, critic_reward: 9.233, revenue_rate: 0.7182, distance: 24.3873, memory: 0.5873, power: 0.9891, lr: 0.000400, took: 148.073s
Batch 60: Reward: 41.4813, Loss: 1031.9767, Revenue: 0.7247, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:3178(99.8%), S2:6(0.2%)], ActorGrad: 35769.9922, CriticGrad: 485.6691, Advantage: μ=31.984, σ=3.103, range=[28.18, 39.83]

📊 Epoch 2 训练统计:
  平均奖励: 38.9644
  平均损失: 939.7000
  平均收益率: 0.6310
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 33.805, revenue_rate: 0.8148, efficiency: 0.7415, distance: 29.9301, memory: 0.5267, power: 0.8980
Test Batch 1/7, reward: 36.248, revenue_rate: 0.8193, efficiency: 0.7865, distance: 31.1097, memory: 0.5786, power: 0.9642
Test Batch 2/7, reward: 36.172, revenue_rate: 0.8052, efficiency: 0.7637, distance: 30.6447, memory: 0.5943, power: 0.9345
Test Batch 3/7, reward: 34.664, revenue_rate: 0.7801, efficiency: 0.7017, distance: 28.6496, memory: 0.5374, power: 0.8928
Test Batch 4/7, reward: 34.209, revenue_rate: 0.7791, efficiency: 0.7158, distance: 30.2413, memory: 0.6092, power: 0.9171
Test Batch 5/7, reward: 33.949, revenue_rate: 0.8077, efficiency: 0.7343, distance: 29.6889, memory: 0.5085, power: 0.9116
Test Batch 6/7, reward: 32.015, revenue_rate: 0.7603, efficiency: 0.6348, distance: 27.1930, memory: 0.3689, power: 0.8488
Test Summary - Avg reward: 34.728±2.298, revenue_rate: 0.7994±0.0624, efficiency: 0.7364, completion_rate: 0.9207, distance: 29.9300, memory: 0.5515, power: 0.9169
Load Balance - Avg balance score: -0.7274±0.0080
Task Distribution by Satellite:
  Satellite 1: 6 tasks (0.03%)
  Satellite 2: 18295 tasks (99.82%)
  Satellite 3: 27 tasks (0.15%)
✅ 验证完成 - Epoch 2, reward: 34.728, revenue_rate: 0.7994, distance: 29.9300, memory: 0.5515, power: 0.9169
  ⚠️ 过拟合: 训练验证差距 = 4.2361
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_04_18_20_18 (验证集奖励: 34.7283)

开始训练 Epoch 3/3
Batch 0: Reward: 41.7695, Loss: 1043.2927, Revenue: 0.7117, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:3172(99.6%), S2:6(0.2%)], ActorGrad: 37400.1406, CriticGrad: 491.2359, Advantage: μ=32.214, σ=2.433, range=[26.12, 36.94]
Batch 5: Reward: 41.6293, Loss: 1050.7806, Revenue: 0.7054, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:3171(99.6%), S2:10(0.3%)], ActorGrad: 35378.7422, CriticGrad: 475.0066, Advantage: μ=32.286, σ=2.996, range=[26.45, 38.32]
Epoch 3, Batch 10/63, loss: 1032.473↑, reward: 41.543↑, critic_reward: 9.545, revenue_rate: 0.7153, distance: 24.1907, memory: 0.6059, power: 0.9999, lr: 0.000400, took: 148.845s
Batch 10: Reward: 42.2101, Loss: 1046.7520, Revenue: 0.7157, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:3174(99.7%), S2:5(0.2%)], ActorGrad: 34511.5000, CriticGrad: 498.7936, Advantage: μ=32.329, σ=1.306, range=[30.31, 35.62]
Batch 15: Reward: 42.5717, Loss: 1077.5620, Revenue: 0.7308, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:3173(99.7%), S2:7(0.2%)], ActorGrad: 33304.0391, CriticGrad: 498.0683, Advantage: μ=32.679, σ=3.207, range=[25.61, 38.97]
Epoch 3, Batch 20/63, loss: 1039.508↓, reward: 41.937↓, critic_reward: 9.796, revenue_rate: 0.7179, distance: 24.0166, memory: 0.6254, power: 0.9993, lr: 0.000400, took: 157.539s
Batch 20: Reward: 41.7154, Loss: 1010.4329, Revenue: 0.7018, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:3173(99.7%), S2:9(0.3%)], ActorGrad: 33476.2070, CriticGrad: 485.5631, Advantage: μ=31.661, σ=2.923, range=[26.32, 38.20]
Batch 25: Reward: 42.8574, Loss: 1078.7417, Revenue: 0.7148, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:3178(99.8%), S2:4(0.1%)], ActorGrad: 34940.7148, CriticGrad: 502.7421, Advantage: μ=32.760, σ=2.428, range=[28.88, 37.13]
Epoch 3, Batch 30/63, loss: 1008.432↓, reward: 41.820↓, critic_reward: 10.194, revenue_rate: 0.7177, distance: 24.1251, memory: 0.6131, power: 1.0009, lr: 0.000400, took: 149.467s
Batch 30: Reward: 41.6287, Loss: 988.3073, Revenue: 0.7124, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:3182(99.9%), S2:1(0.0%)], ActorGrad: 32674.4023, CriticGrad: 491.5054, Advantage: μ=31.248, σ=3.554, range=[25.58, 38.45]
Batch 35: Reward: 42.4157, Loss: 1043.1456, Revenue: 0.7221, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:3184(100.0%), S2:0(0.0%)], ActorGrad: 33625.2773, CriticGrad: 494.4227, Advantage: μ=32.164, σ=3.030, range=[27.32, 37.49]
Epoch 3, Batch 40/63, loss: 1004.962↓, reward: 41.778↓, critic_reward: 10.207, revenue_rate: 0.7165, distance: 23.8634, memory: 0.6166, power: 0.9963, lr: 0.000400, took: 155.958s
Batch 40: Reward: 40.9288, Loss: 936.9981, Revenue: 0.7058, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:3181(99.9%), S2:1(0.0%)], ActorGrad: 33051.1758, CriticGrad: 471.3609, Advantage: μ=30.535, σ=2.219, range=[27.22, 35.93]
Batch 45: Reward: 42.5128, Loss: 1041.8922, Revenue: 0.7153, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:3183(100.0%), S2:0(0.0%)], ActorGrad: 35107.2148, CriticGrad: 490.6523, Advantage: μ=32.157, σ=2.892, range=[28.42, 37.53]
Epoch 3, Batch 50/63, loss: 981.645↑, reward: 41.835↑, critic_reward: 10.626, revenue_rate: 0.7137, distance: 23.6307, memory: 0.6183, power: 0.9946, lr: 0.000400, took: 149.256s
Batch 50: Reward: 40.9161, Loss: 940.6379, Revenue: 0.7105, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:3183(100.0%), S2:1(0.0%)], ActorGrad: 32152.0449, CriticGrad: 468.1155, Advantage: μ=30.528, σ=3.047, range=[25.07, 34.68]
Batch 55: Reward: 41.7983, Loss: 973.4633, Revenue: 0.7020, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:3181(99.9%), S2:2(0.1%)], ActorGrad: 29563.8203, CriticGrad: 491.2463, Advantage: μ=31.044, σ=3.225, range=[23.24, 37.03]
Epoch 3, Batch 60/63, loss: 947.195↑, reward: 41.500↑, critic_reward: 10.845, revenue_rate: 0.7139, distance: 23.8671, memory: 0.6023, power: 0.9968, lr: 0.000400, took: 150.053s
Batch 60: Reward: 42.6945, Loss: 1019.9583, Revenue: 0.7289, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:3184(100.0%), S2:0(0.0%)], ActorGrad: 35207.0273, CriticGrad: 498.3528, Advantage: μ=31.768, σ=3.385, range=[23.89, 36.50]

📊 Epoch 3 训练统计:
  平均奖励: 41.7937
  平均损失: 1004.1698
  平均收益率: 0.7163
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 34.151, revenue_rate: 0.8102, efficiency: 0.7363, distance: 29.3973, memory: 0.5642, power: 0.8877
Test Batch 1/7, reward: 33.917, revenue_rate: 0.7912, efficiency: 0.7079, distance: 28.4669, memory: 0.6005, power: 0.9019
Test Batch 2/7, reward: 36.945, revenue_rate: 0.8675, efficiency: 0.8366, distance: 29.9730, memory: 0.6288, power: 0.9637
Test Batch 3/7, reward: 34.791, revenue_rate: 0.8266, efficiency: 0.7685, distance: 30.6673, memory: 0.6364, power: 0.9232
Test Batch 4/7, reward: 35.524, revenue_rate: 0.8248, efficiency: 0.7787, distance: 30.3407, memory: 0.6084, power: 0.9449
Test Batch 5/7, reward: 33.511, revenue_rate: 0.8274, efficiency: 0.7401, distance: 28.4031, memory: 0.5657, power: 0.8956
Test Batch 6/7, reward: 33.343, revenue_rate: 0.8371, efficiency: 0.7575, distance: 31.6143, memory: 0.6062, power: 0.9258
Test Summary - Avg reward: 34.748±2.356, revenue_rate: 0.8251±0.0638, efficiency: 0.7612, completion_rate: 0.9220, distance: 29.6243, memory: 0.6009, power: 0.9197
Load Balance - Avg balance score: -0.7305±0.0048
Task Distribution by Satellite:
  Satellite 1: 5 tasks (0.03%)
  Satellite 2: 18341 tasks (99.94%)
  Satellite 3: 6 tasks (0.03%)
✅ 验证完成 - Epoch 3, reward: 34.748, revenue_rate: 0.8251, distance: 29.6243, memory: 0.6009, power: 0.9197
  ⚠️ 过拟合: 训练验证差距 = 7.0458
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_04_18_20_18 (验证集奖励: 34.7478)
训练完成

开始测试模型...
Test Batch 0/7, reward: 35.595, revenue_rate: 0.8468, efficiency: 0.7703, distance: 29.4627, memory: 0.5457, power: 0.8746
Test Batch 1/7, reward: 35.890, revenue_rate: 0.8150, efficiency: 0.7702, distance: 30.3618, memory: 0.6088, power: 0.9339
Test Batch 2/7, reward: 34.886, revenue_rate: 0.8141, efficiency: 0.7607, distance: 29.0737, memory: 0.5902, power: 0.9217
Test Batch 3/7, reward: 35.232, revenue_rate: 0.8399, efficiency: 0.7889, distance: 30.2051, memory: 0.6210, power: 0.9506
Test Batch 4/7, reward: 33.878, revenue_rate: 0.7988, efficiency: 0.7100, distance: 28.3651, memory: 0.5680, power: 0.8797
Test Batch 5/7, reward: 34.198, revenue_rate: 0.8417, efficiency: 0.7654, distance: 29.3224, memory: 0.5854, power: 0.9094
Test Batch 6/7, reward: 32.932, revenue_rate: 0.7204, efficiency: 0.6349, distance: 27.7522, memory: 0.5668, power: 0.8720
Test Summary - Avg reward: 34.866±2.240, revenue_rate: 0.8218±0.0665, efficiency: 0.7559, completion_rate: 0.9195, distance: 29.3966, memory: 0.5857, power: 0.9101
Load Balance - Avg balance score: -0.7306±0.0043
Task Distribution by Satellite:
  Satellite 1: 7 tasks (0.04%)
  Satellite 2: 18294 tasks (99.95%)
  Satellite 3: 3 tasks (0.02%)
测试完成 - 平均奖励: 34.866, 平均星座收益率: 0.8218
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_cooperative_2025_08_04_18_20_18
   平均奖励: 34.8658
   收益率: 0.8218

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 200
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 16
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_04_19_03_14
使用模型: gpn_transformer
Actor参数数量: 5,929,732
Critic参数数量: 2,942,081

开始训练 Epoch 1/3
Batch 0: Reward: 17.1659, Loss: 277.3945, Revenue: 0.2442, LoadBalance: 0.3684, Tasks: [S0:295(33.5%), S1:115(13.1%), S2:470(53.4%)], ActorGrad: 3902.5378, CriticGrad: 1750.1025, Advantage: μ=16.560, σ=1.835, range=[13.07, 18.78]
Batch 5: Reward: 37.5720, Loss: 1019.0547, Revenue: 0.7667, LoadBalance: 0.0000, Tasks: [S0:26(0.8%), S1:3078(98.7%), S2:16(0.5%)], ActorGrad: 20025.2480, CriticGrad: 690.1684, Advantage: μ=31.873, σ=1.832, range=[29.41, 35.17]
Epoch 1, Batch 10/63, loss: 752.650↑, reward: 30.962↑, critic_reward: 4.639, revenue_rate: 0.6230, distance: 23.3676, memory: 0.5016, power: 0.7624, lr: 0.000400, took: 123.939s
Batch 10: Reward: 38.1320, Loss: 1042.9598, Revenue: 0.8063, LoadBalance: 0.0000, Tasks: [S0:12(0.4%), S1:3167(99.5%), S2:5(0.2%)], ActorGrad: 26685.3770, CriticGrad: 501.5162, Advantage: μ=32.222, σ=2.233, range=[28.92, 36.43]
Batch 15: Reward: 40.2782, Loss: 1161.1105, Revenue: 0.7493, LoadBalance: 0.0000, Tasks: [S0:13(0.4%), S1:3157(99.2%), S2:14(0.4%)], ActorGrad: 45437.8867, CriticGrad: 518.7567, Advantage: μ=33.989, σ=2.495, range=[29.09, 37.71]
Epoch 1, Batch 20/63, loss: 1079.729↓, reward: 38.997↓, critic_reward: 6.251, revenue_rate: 0.7521, distance: 27.7968, memory: 0.6113, power: 0.9742, lr: 0.000400, took: 147.782s
Batch 20: Reward: 36.9834, Loss: 936.9865, Revenue: 0.6420, LoadBalance: 0.0000, Tasks: [S0:78(2.7%), S1:2760(95.3%), S2:58(2.0%)], ActorGrad: 15433.5547, CriticGrad: 444.5791, Advantage: μ=30.466, σ=3.067, range=[25.38, 34.83]
Batch 25: Reward: 36.3997, Loss: 880.1563, Revenue: 0.7173, LoadBalance: 0.0000, Tasks: [S0:63(2.2%), S1:37(1.3%), S2:2796(96.5%)], ActorGrad: 16780.1406, CriticGrad: 426.6239, Advantage: μ=29.593, σ=2.167, range=[26.76, 34.31]
Epoch 1, Batch 30/63, loss: 676.131↑, reward: 31.790↑, critic_reward: 6.736, revenue_rate: 0.6145, distance: 23.2631, memory: 0.4870, power: 0.7822, lr: 0.000400, took: 157.163s
Batch 30: Reward: 37.7702, Loss: 948.4917, Revenue: 0.8295, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:6(0.2%), S2:3174(99.7%)], ActorGrad: 24695.0898, CriticGrad: 435.0566, Advantage: μ=30.723, σ=2.212, range=[26.69, 35.38]
Batch 35: Reward: 39.1314, Loss: 1044.5847, Revenue: 0.7981, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:21(0.7%), S2:3157(99.2%)], ActorGrad: 26860.7070, CriticGrad: 449.5306, Advantage: μ=32.211, σ=2.734, range=[27.94, 36.15]
Epoch 1, Batch 40/63, loss: 1021.723↑, reward: 38.909↑, critic_reward: 7.059, revenue_rate: 0.7913, distance: 28.6808, memory: 0.6094, power: 0.9886, lr: 0.000400, took: 134.366s
Batch 40: Reward: 39.8080, Loss: 1068.9727, Revenue: 0.7170, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:139(4.4%), S2:3009(95.5%)], ActorGrad: 33043.1562, CriticGrad: 460.9106, Advantage: μ=32.549, σ=3.189, range=[27.03, 39.65]
Batch 45: Reward: 40.8683, Loss: 1102.4475, Revenue: 0.7676, LoadBalance: 0.0000, Tasks: [S0:16(0.5%), S1:25(0.8%), S2:3143(98.7%)], ActorGrad: 44437.8633, CriticGrad: 485.7847, Advantage: μ=33.120, σ=2.430, range=[28.84, 38.17]
Epoch 1, Batch 50/63, loss: 1040.079↑, reward: 39.512↑, critic_reward: 7.409, revenue_rate: 0.7131, distance: 25.7976, memory: 0.5748, power: 0.9559, lr: 0.000400, took: 122.248s
Batch 50: Reward: 39.9789, Loss: 1045.0583, Revenue: 0.7600, LoadBalance: 0.0000, Tasks: [S0:19(0.6%), S1:4(0.1%), S2:3161(99.3%)], ActorGrad: 44168.2422, CriticGrad: 464.3978, Advantage: μ=32.275, σ=1.903, range=[29.35, 36.03]
Batch 55: Reward: 40.2192, Loss: 1065.4907, Revenue: 0.7644, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3181(99.9%)], ActorGrad: 60043.0469, CriticGrad: 466.0609, Advantage: μ=32.534, σ=2.735, range=[28.38, 38.51]
Epoch 1, Batch 60/63, loss: 1101.240↑, reward: 40.778↑, critic_reward: 7.691, revenue_rate: 0.7766, distance: 25.9429, memory: 0.6536, power: 0.9960, lr: 0.000400, took: 124.122s
Batch 60: Reward: 39.8572, Loss: 1019.1425, Revenue: 0.7785, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 53529.5195, CriticGrad: 464.6823, Advantage: μ=31.846, σ=2.298, range=[27.57, 35.26]

📊 Epoch 1 训练统计:
  平均奖励: 37.0011
  平均损失: 951.0443
  平均收益率: 0.7152
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 35.950, revenue_rate: 0.8766, efficiency: 0.8456, distance: 31.4628, memory: 0.6221, power: 0.9483
Test Batch 1/7, reward: 35.259, revenue_rate: 0.8775, efficiency: 0.8202, distance: 30.0105, memory: 0.6319, power: 0.9475
Test Batch 2/7, reward: 36.015, revenue_rate: 0.8704, efficiency: 0.8179, distance: 30.0485, memory: 0.5605, power: 0.9335
Test Batch 3/7, reward: 35.661, revenue_rate: 0.8749, efficiency: 0.8218, distance: 30.2102, memory: 0.6337, power: 0.9358
Test Batch 4/7, reward: 34.364, revenue_rate: 0.8687, efficiency: 0.7949, distance: 29.3525, memory: 0.5565, power: 0.9154
Test Batch 5/7, reward: 34.409, revenue_rate: 0.8653, efficiency: 0.8169, distance: 30.6501, memory: 0.6293, power: 0.9559
Test Batch 6/7, reward: 34.137, revenue_rate: 0.8612, efficiency: 0.7923, distance: 29.7477, memory: 0.6054, power: 0.8984
Test Summary - Avg reward: 35.231±2.010, revenue_rate: 0.8718±0.0273, efficiency: 0.8185, completion_rate: 0.9388, distance: 30.2674, memory: 0.6057, power: 0.9378
Load Balance - Avg balance score: -0.7311±0.0035
Task Distribution by Satellite:
  Satellite 1: 5 tasks (0.03%)
  Satellite 2: 2 tasks (0.01%)
  Satellite 3: 18677 tasks (99.96%)
✅ 验证完成 - Epoch 1, reward: 35.231, revenue_rate: 0.8718, distance: 30.2674, memory: 0.6057, power: 0.9378
  ✅ 训练验证差距正常: 1.7703
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_competitive_2025_08_04_19_03_14 (验证集奖励: 35.2308)

开始训练 Epoch 2/3
Batch 0: Reward: 40.2709, Loss: 1048.4019, Revenue: 0.7872, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:0(0.0%), S2:3181(99.9%)], ActorGrad: 57121.3633, CriticGrad: 464.8864, Advantage: μ=32.334, σ=1.766, range=[29.87, 35.42]
Batch 5: Reward: 40.0622, Loss: 1031.5703, Revenue: 0.8174, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 53383.1836, CriticGrad: 457.8937, Advantage: μ=32.046, σ=2.215, range=[29.03, 35.62]
Epoch 2, Batch 10/63, loss: 1034.155↓, reward: 40.138↓, critic_reward: 8.075, revenue_rate: 0.7999, distance: 26.4003, memory: 0.6460, power: 1.0002, lr: 0.000400, took: 131.677s
Batch 10: Reward: 40.7099, Loss: 1065.7242, Revenue: 0.7772, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:0(0.0%), S2:3178(99.8%)], ActorGrad: 42558.1797, CriticGrad: 478.1378, Advantage: μ=32.472, σ=3.468, range=[27.10, 38.36]
Batch 15: Reward: 39.8296, Loss: 1006.5758, Revenue: 0.8081, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:0(0.0%), S2:3178(99.8%)], ActorGrad: 37736.7578, CriticGrad: 459.0245, Advantage: μ=31.661, σ=2.098, range=[26.92, 34.58]
Epoch 2, Batch 20/63, loss: 1019.456↓, reward: 40.164↓, critic_reward: 8.332, revenue_rate: 0.8167, distance: 27.1671, memory: 0.6402, power: 0.9928, lr: 0.000400, took: 123.947s
Batch 20: Reward: 40.8190, Loss: 1064.2484, Revenue: 0.8299, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3181(99.9%)], ActorGrad: 40832.3594, CriticGrad: 469.2231, Advantage: μ=32.503, σ=2.880, range=[27.04, 37.47]
Batch 25: Reward: 40.7467, Loss: 1045.2424, Revenue: 0.7955, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:0(0.0%), S2:3181(99.9%)], ActorGrad: 52215.4180, CriticGrad: 469.3888, Advantage: μ=32.262, σ=2.173, range=[28.76, 37.86]
Epoch 2, Batch 30/63, loss: 1027.466↓, reward: 40.395↓, critic_reward: 8.440, revenue_rate: 0.7972, distance: 26.6242, memory: 0.6429, power: 0.9971, lr: 0.000400, took: 123.999s
Batch 30: Reward: 40.7370, Loss: 1028.0641, Revenue: 0.7744, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:0(0.0%), S2:3181(99.9%)], ActorGrad: 54852.2344, CriticGrad: 480.0957, Advantage: μ=31.961, σ=2.639, range=[28.42, 37.80]
Batch 35: Reward: 39.9518, Loss: 967.4462, Revenue: 0.8135, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:1(0.0%), S2:3180(99.9%)], ActorGrad: 54994.5430, CriticGrad: 468.5583, Advantage: μ=31.005, σ=2.553, range=[27.46, 36.61]
Epoch 2, Batch 40/63, loss: 1005.642↑, reward: 40.375↑, critic_reward: 8.766, revenue_rate: 0.7827, distance: 25.8673, memory: 0.6509, power: 0.9941, lr: 0.000400, took: 124.825s
Batch 40: Reward: 42.5285, Loss: 1140.8420, Revenue: 0.7183, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3182(99.9%)], ActorGrad: 118644.9062, CriticGrad: 500.1859, Advantage: μ=33.602, σ=3.540, range=[28.88, 41.53]
Batch 45: Reward: 40.3648, Loss: 976.2443, Revenue: 0.7921, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:1(0.0%), S2:3179(99.8%)], ActorGrad: 52652.3594, CriticGrad: 474.3384, Advantage: μ=31.152, σ=2.491, range=[27.68, 35.57]
Epoch 2, Batch 50/63, loss: 1030.315↓, reward: 40.930↓, critic_reward: 8.949, revenue_rate: 0.7760, distance: 25.8501, memory: 0.6422, power: 0.9923, lr: 0.000400, took: 125.358s
Batch 50: Reward: 40.0803, Loss: 956.4274, Revenue: 0.7641, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:16(0.5%), S2:3166(99.4%)], ActorGrad: 33265.2227, CriticGrad: 465.5379, Advantage: μ=30.850, σ=2.237, range=[26.78, 34.07]
Batch 55: Reward: 39.6575, Loss: 953.2352, Revenue: 0.8208, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:10(0.3%), S2:3174(99.7%)], ActorGrad: 31282.2012, CriticGrad: 446.7920, Advantage: μ=30.798, σ=2.240, range=[26.70, 34.71]
Epoch 2, Batch 60/63, loss: 967.478↑, reward: 40.185↑, critic_reward: 9.189, revenue_rate: 0.7784, distance: 27.2105, memory: 0.6262, power: 0.9873, lr: 0.000400, took: 124.327s
Batch 60: Reward: 41.1550, Loss: 1033.2502, Revenue: 0.7582, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 72029.9219, CriticGrad: 468.8969, Advantage: μ=32.029, σ=2.802, range=[27.75, 38.33]

📊 Epoch 2 训练统计:
  平均奖励: 40.4218
  平均损失: 1015.5781
  平均收益率: 0.7909
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 35.043, revenue_rate: 0.8824, efficiency: 0.8290, distance: 30.1855, memory: 0.6107, power: 0.9261
Test Batch 1/7, reward: 35.529, revenue_rate: 0.8812, efficiency: 0.8283, distance: 30.7157, memory: 0.6285, power: 0.9491
Test Batch 2/7, reward: 35.226, revenue_rate: 0.8718, efficiency: 0.8015, distance: 29.0291, memory: 0.6001, power: 0.9154
Test Batch 3/7, reward: 35.493, revenue_rate: 0.8774, efficiency: 0.8204, distance: 30.0820, memory: 0.6181, power: 0.9290
Test Batch 4/7, reward: 34.280, revenue_rate: 0.8725, efficiency: 0.8027, distance: 29.5351, memory: 0.6134, power: 0.9206
Test Batch 5/7, reward: 34.461, revenue_rate: 0.8581, efficiency: 0.8063, distance: 30.5533, memory: 0.5903, power: 0.9518
Test Batch 6/7, reward: 33.858, revenue_rate: 0.8515, efficiency: 0.7791, distance: 31.0180, memory: 0.6082, power: 0.9065
Test Summary - Avg reward: 34.959±2.007, revenue_rate: 0.8730±0.0262, efficiency: 0.8133, completion_rate: 0.9315, distance: 30.0568, memory: 0.6101, power: 0.9310
Load Balance - Avg balance score: -0.7315±0.0028
Task Distribution by Satellite:
  Satellite 1: 2 tasks (0.01%)
  Satellite 2: 2 tasks (0.01%)
  Satellite 3: 18532 tasks (99.98%)
✅ 验证完成 - Epoch 2, reward: 34.959, revenue_rate: 0.8730, distance: 30.0568, memory: 0.6101, power: 0.9310
  ⚠️ 过拟合: 训练验证差距 = 5.4623

开始训练 Epoch 3/3
Batch 0: Reward: 40.9138, Loss: 1018.4539, Revenue: 0.8083, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 61087.2891, CriticGrad: 465.1479, Advantage: μ=31.764, σ=3.179, range=[24.42, 36.68]
Batch 5: Reward: 41.1242, Loss: 995.3804, Revenue: 0.7489, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 75970.7422, CriticGrad: 482.3615, Advantage: μ=31.448, σ=2.609, range=[26.94, 35.33]
Epoch 3, Batch 10/63, loss: 1011.027↑, reward: 41.162↑, critic_reward: 9.485, revenue_rate: 0.7661, distance: 24.9859, memory: 0.6507, power: 1.0003, lr: 0.000400, took: 133.908s
Batch 10: Reward: 41.3953, Loss: 1008.5776, Revenue: 0.7756, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 60796.3477, CriticGrad: 486.7786, Advantage: μ=31.712, σ=1.765, range=[28.87, 35.06]
Batch 15: Reward: 40.4184, Loss: 954.4055, Revenue: 0.8246, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3182(99.9%)], ActorGrad: 35439.8906, CriticGrad: 458.5693, Advantage: μ=30.839, σ=1.887, range=[27.26, 34.93]
Epoch 3, Batch 20/63, loss: 937.916↓, reward: 40.311↓, critic_reward: 9.770, revenue_rate: 0.8114, distance: 26.7914, memory: 0.6597, power: 0.9952, lr: 0.000400, took: 124.029s
Batch 20: Reward: 40.9184, Loss: 997.7786, Revenue: 0.8117, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3182(99.9%)], ActorGrad: 45087.8320, CriticGrad: 458.4217, Advantage: μ=31.502, σ=2.396, range=[27.93, 35.91]
Batch 25: Reward: 41.1336, Loss: 975.7012, Revenue: 0.7918, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 58928.2656, CriticGrad: 478.1480, Advantage: μ=31.125, σ=2.724, range=[27.21, 35.43]
Epoch 3, Batch 30/63, loss: 949.700↓, reward: 40.646↓, critic_reward: 9.927, revenue_rate: 0.7963, distance: 26.1254, memory: 0.6378, power: 0.9972, lr: 0.000400, took: 124.363s
Batch 30: Reward: 41.7448, Loss: 995.4678, Revenue: 0.7912, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 61897.9531, CriticGrad: 492.0337, Advantage: μ=31.505, σ=1.755, range=[27.80, 33.95]
Batch 35: Reward: 39.9039, Loss: 884.6874, Revenue: 0.8455, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 27608.4824, CriticGrad: 460.5130, Advantage: μ=29.694, σ=1.770, range=[26.66, 33.13]
Epoch 3, Batch 40/63, loss: 879.744↓, reward: 39.819↓, critic_reward: 10.270, revenue_rate: 0.8175, distance: 27.2212, memory: 0.6513, power: 0.9971, lr: 0.000400, took: 124.773s
Batch 40: Reward: 38.5414, Loss: 805.0880, Revenue: 0.8550, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 23785.8730, CriticGrad: 437.7612, Advantage: μ=28.259, σ=2.632, range=[23.56, 32.37]
Batch 45: Reward: 40.7632, Loss: 937.4703, Revenue: 0.8238, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 48365.5195, CriticGrad: 465.6635, Advantage: μ=30.482, σ=2.975, range=[26.18, 35.72]
Epoch 3, Batch 50/63, loss: 875.046↑, reward: 39.925↑, critic_reward: 10.456, revenue_rate: 0.8283, distance: 27.3422, memory: 0.6268, power: 0.9921, lr: 0.000400, took: 125.032s
Batch 50: Reward: 39.0245, Loss: 800.3478, Revenue: 0.8269, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 45025.9219, CriticGrad: 451.9581, Advantage: μ=28.215, σ=2.139, range=[23.88, 31.58]
Batch 55: Reward: 41.4525, Loss: 955.8127, Revenue: 0.7486, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 68301.3125, CriticGrad: 480.6225, Advantage: μ=30.790, σ=2.883, range=[26.88, 37.10]
Epoch 3, Batch 60/63, loss: 893.674↑, reward: 40.572↑, critic_reward: 10.791, revenue_rate: 0.7779, distance: 25.4357, memory: 0.6384, power: 0.9906, lr: 0.000400, took: 124.907s
Batch 60: Reward: 42.1774, Loss: 981.8533, Revenue: 0.7411, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3184(100.0%)], ActorGrad: 77791.2188, CriticGrad: 500.4591, Advantage: μ=31.193, σ=3.073, range=[25.06, 36.04]

📊 Epoch 3 训练统计:
  平均奖励: 40.4491
  平均损失: 925.2584
  平均收益率: 0.7980
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 35.467, revenue_rate: 0.8738, efficiency: 0.8426, distance: 31.9478, memory: 0.6003, power: 0.9445
Test Batch 1/7, reward: 35.552, revenue_rate: 0.8711, efficiency: 0.8188, distance: 30.2325, memory: 0.6128, power: 0.9440
Test Batch 2/7, reward: 35.448, revenue_rate: 0.8774, efficiency: 0.8247, distance: 30.5719, memory: 0.6177, power: 0.9321
Test Batch 3/7, reward: 35.413, revenue_rate: 0.8732, efficiency: 0.8208, distance: 30.1308, memory: 0.6748, power: 0.9343
Test Batch 4/7, reward: 34.645, revenue_rate: 0.8707, efficiency: 0.8097, distance: 29.8566, memory: 0.6330, power: 0.9322
Test Batch 5/7, reward: 35.063, revenue_rate: 0.8652, efficiency: 0.8260, distance: 30.5126, memory: 0.6013, power: 0.9654
Test Batch 6/7, reward: 34.186, revenue_rate: 0.8585, efficiency: 0.7855, distance: 29.6036, memory: 0.6033, power: 0.8893
Test Summary - Avg reward: 35.222±2.058, revenue_rate: 0.8713±0.0300, efficiency: 0.8223, completion_rate: 0.9436, distance: 30.5045, memory: 0.6225, power: 0.9400
Load Balance - Avg balance score: -0.7318±0.0019
Task Distribution by Satellite:
  Satellite 1: 1 tasks (0.01%)
  Satellite 2: 1 tasks (0.01%)
  Satellite 3: 18774 tasks (99.99%)
✅ 验证完成 - Epoch 3, reward: 35.222, revenue_rate: 0.8713, distance: 30.5045, memory: 0.6225, power: 0.9400
  ⚠️ 过拟合: 训练验证差距 = 5.2275
训练完成

开始测试模型...
Test Batch 0/7, reward: 35.822, revenue_rate: 0.8740, efficiency: 0.8216, distance: 30.6525, memory: 0.5533, power: 0.9116
Test Batch 1/7, reward: 34.857, revenue_rate: 0.8732, efficiency: 0.8120, distance: 29.9590, memory: 0.6513, power: 0.9298
Test Batch 2/7, reward: 34.197, revenue_rate: 0.8524, efficiency: 0.7797, distance: 28.8197, memory: 0.5626, power: 0.9003
Test Batch 3/7, reward: 34.036, revenue_rate: 0.8564, efficiency: 0.7876, distance: 30.3747, memory: 0.6243, power: 0.9333
Test Batch 4/7, reward: 34.849, revenue_rate: 0.8702, efficiency: 0.8179, distance: 30.4988, memory: 0.5874, power: 0.9273
Test Batch 5/7, reward: 35.013, revenue_rate: 0.8789, efficiency: 0.8347, distance: 30.6565, memory: 0.6626, power: 0.9431
Test Batch 6/7, reward: 33.979, revenue_rate: 0.8557, efficiency: 0.7723, distance: 28.9734, memory: 0.5970, power: 0.8840
Test Summary - Avg reward: 34.763±2.036, revenue_rate: 0.8670±0.0336, efficiency: 0.8075, completion_rate: 0.9312, distance: 30.1127, memory: 0.6065, power: 0.9226
Load Balance - Avg balance score: -0.7319±0.0014
Task Distribution by Satellite:
  Satellite 1: 0 tasks (0.00%)
  Satellite 2: 1 tasks (0.01%)
  Satellite 3: 18527 tasks (99.99%)
测试完成 - 平均奖励: 34.763, 平均星座收益率: 0.8670
✅ 模式 COMPETITIVE 训练完成
   保存路径: constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_competitive_2025_08_04_19_03_14
   平均奖励: 34.7630
   收益率: 0.8670

🚀 [3/3] 开始训练模式: HYBRID

============================================================
开始训练星座模式: HYBRID
============================================================
constellation_smp: 200
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 16
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: hybrid
verbose: True
2025_08_04_19_49_15
使用模型: gpn_transformer
Actor参数数量: 5,929,732
Critic参数数量: 2,942,081

开始训练 Epoch 1/3
Batch 0: Reward: 16.6328, Loss: 285.7186, Revenue: 0.1743, LoadBalance: 0.6892, Tasks: [S0:221(31.4%), S1:224(31.8%), S2:259(36.8%)], ActorGrad: 10542.7959, CriticGrad: 1489.0417, Advantage: μ=16.764, σ=2.235, range=[13.47, 21.29]
Batch 5: Reward: 25.5845, Loss: 404.5359, Revenue: 0.4340, LoadBalance: 0.0000, Tasks: [S0:122(6.7%), S1:128(7.0%), S2:1574(86.3%)], ActorGrad: 9714.9814, CriticGrad: 389.0941, Advantage: μ=20.013, σ=2.071, range=[17.41, 24.57]
Epoch 1, Batch 10/63, loss: 457.472↑, reward: 25.333↑, critic_reward: 4.571, revenue_rate: 0.3941, distance: 15.1018, memory: 0.3152, power: 0.5234, lr: 0.000400, took: 60.963s
Batch 10: Reward: 33.4178, Loss: 749.8197, Revenue: 0.6062, LoadBalance: 0.0000, Tasks: [S0:84(3.1%), S1:30(1.1%), S2:2574(95.8%)], ActorGrad: 20621.1641, CriticGrad: 457.3368, Advantage: μ=27.265, σ=2.620, range=[23.19, 33.91]
Batch 15: Reward: 16.0652, Loss: 94.1801, Revenue: 0.1661, LoadBalance: 0.3254, Tasks: [S0:132(18.3%), S1:165(22.9%), S2:423(58.8%)], ActorGrad: 2861.7073, CriticGrad: 146.2404, Advantage: μ=9.564, σ=1.699, range=[5.86, 12.98]
Epoch 1, Batch 20/63, loss: 298.642↓, reward: 22.349↓, critic_reward: 6.468, revenue_rate: 0.3365, distance: 12.7543, memory: 0.2451, power: 0.4485, lr: 0.000400, took: 49.001s
Batch 20: Reward: 16.1508, Loss: 87.8298, Revenue: 0.1619, LoadBalance: 0.4908, Tasks: [S0:131(18.6%), S1:223(31.7%), S2:350(49.7%)], ActorGrad: 2626.5715, CriticGrad: 140.2529, Advantage: μ=9.273, σ=1.401, range=[7.19, 12.09]
Batch 25: Reward: 29.2022, Loss: 495.5330, Revenue: 0.4507, LoadBalance: 0.0307, Tasks: [S0:91(4.7%), S1:474(24.3%), S2:1387(71.1%)], ActorGrad: 9960.0293, CriticGrad: 328.6530, Advantage: μ=22.168, σ=2.090, range=[18.47, 25.50]
Epoch 1, Batch 30/63, loss: 272.668↑, reward: 22.456↑, critic_reward: 6.897, revenue_rate: 0.3151, distance: 11.8434, memory: 0.2602, power: 0.4314, lr: 0.000400, took: 46.714s
Batch 30: Reward: 31.0853, Loss: 579.1068, Revenue: 0.5347, LoadBalance: 0.0000, Tasks: [S0:69(2.8%), S1:2314(93.3%), S2:97(3.9%)], ActorGrad: 14952.9521, CriticGrad: 357.2870, Advantage: μ=23.946, σ=2.463, range=[19.69, 27.95]
Batch 35: Reward: 38.1607, Loss: 961.4606, Revenue: 0.6785, LoadBalance: 0.0000, Tasks: [S0:22(0.7%), S1:2861(96.1%), S2:93(3.1%)], ActorGrad: 21742.1094, CriticGrad: 452.3960, Advantage: μ=30.860, σ=3.119, range=[26.14, 37.37]
Epoch 1, Batch 40/63, loss: 795.004↑, reward: 35.215↑, critic_reward: 7.209, revenue_rate: 0.6275, distance: 24.6690, memory: 0.5564, power: 0.8612, lr: 0.000400, took: 97.830s
Batch 40: Reward: 36.1855, Loss: 846.1364, Revenue: 0.6390, LoadBalance: 0.0000, Tasks: [S0:11(0.4%), S1:2720(96.6%), S2:85(3.0%)], ActorGrad: 22797.4043, CriticGrad: 402.9536, Advantage: μ=28.934, σ=3.096, range=[23.11, 34.16]
Batch 45: Reward: 39.0782, Loss: 1005.7546, Revenue: 0.7220, LoadBalance: 0.0000, Tasks: [S0:11(0.3%), S1:3139(98.6%), S2:34(1.1%)], ActorGrad: 24241.9141, CriticGrad: 449.0292, Advantage: μ=31.574, σ=3.074, range=[23.31, 36.73]
Epoch 1, Batch 50/63, loss: 1010.122↑, reward: 39.133↑, critic_reward: 7.495, revenue_rate: 0.7172, distance: 27.4133, memory: 0.6147, power: 0.9728, lr: 0.000400, took: 117.333s
Batch 50: Reward: 38.0180, Loss: 940.1365, Revenue: 0.7157, LoadBalance: 0.0000, Tasks: [S0:33(1.1%), S1:2973(98.3%), S2:18(0.6%)], ActorGrad: 21307.1914, CriticGrad: 433.9115, Advantage: μ=30.573, σ=2.408, range=[26.70, 35.55]
Batch 55: Reward: 40.5262, Loss: 1065.8073, Revenue: 0.7933, LoadBalance: 0.0000, Tasks: [S0:33(1.0%), S1:3143(98.7%), S2:8(0.3%)], ActorGrad: 26930.9863, CriticGrad: 473.9134, Advantage: μ=32.596, σ=1.883, range=[29.51, 35.60]
Epoch 1, Batch 60/63, loss: 1015.922↑, reward: 39.495↑, critic_reward: 7.761, revenue_rate: 0.7560, distance: 27.7816, memory: 0.6217, power: 0.9868, lr: 0.000400, took: 121.189s
Batch 60: Reward: 39.0691, Loss: 989.5370, Revenue: 0.8160, LoadBalance: 0.0000, Tasks: [S0:12(0.4%), S1:3171(99.6%), S2:1(0.0%)], ActorGrad: 27458.0566, CriticGrad: 448.5487, Advantage: μ=31.355, σ=2.608, range=[26.02, 35.78]

📊 Epoch 1 训练统计:
  平均奖励: 31.1121
  平均损失: 660.4390
  平均收益率: 0.5377
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 30.882, revenue_rate: 0.8513, efficiency: 0.7826, distance: 29.9776, memory: 0.6071, power: 0.9072
Test Batch 1/7, reward: 35.079, revenue_rate: 0.8084, efficiency: 0.7385, distance: 28.9412, memory: 0.5473, power: 0.9234
Test Batch 2/7, reward: 32.257, revenue_rate: 0.8556, efficiency: 0.7912, distance: 29.6764, memory: 0.6298, power: 0.9213
Test Batch 3/7, reward: 35.794, revenue_rate: 0.8398, efficiency: 0.7805, distance: 29.5486, memory: 0.6179, power: 0.9184
Test Batch 4/7, reward: 34.764, revenue_rate: 0.8433, efficiency: 0.7959, distance: 31.0828, memory: 0.6620, power: 0.9480
Test Batch 5/7, reward: 34.239, revenue_rate: 0.8518, efficiency: 0.7751, distance: 28.9376, memory: 0.5640, power: 0.9243
Test Batch 6/7, reward: 34.266, revenue_rate: 0.8518, efficiency: 0.7709, distance: 28.9602, memory: 0.5965, power: 0.9147
Test Summary - Avg reward: 33.853±2.496, revenue_rate: 0.8421±0.0518, efficiency: 0.7771, completion_rate: 0.9227, distance: 29.6647, memory: 0.6044, power: 0.9234
Load Balance - Avg balance score: -0.7240±0.0110
Task Distribution by Satellite:
  Satellite 1: 51 tasks (0.28%)
  Satellite 2: 18311 tasks (99.69%)
  Satellite 3: 6 tasks (0.03%)
✅ 验证完成 - Epoch 1, reward: 33.853, revenue_rate: 0.8421, distance: 29.6647, memory: 0.6044, power: 0.9234
  ⚠️ 欠拟合: 训练验证差距 = -2.7408
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_hybrid_2025_08_04_19_49_15 (验证集奖励: 33.8528)

开始训练 Epoch 2/3
Batch 0: Reward: 39.9853, Loss: 1025.8727, Revenue: 0.7945, LoadBalance: 0.0000, Tasks: [S0:22(0.7%), S1:3159(99.2%), S2:3(0.1%)], ActorGrad: 27051.4316, CriticGrad: 465.0880, Advantage: μ=31.916, σ=2.784, range=[25.79, 36.04]
Batch 5: Reward: 19.1821, Loss: 128.3874, Revenue: 0.2673, LoadBalance: 0.0504, Tasks: [S0:229(19.1%), S1:857(71.4%), S2:114(9.5%)], ActorGrad: 3405.0254, CriticGrad: 159.6555, Advantage: μ=11.146, σ=2.107, range=[7.90, 16.09]
Epoch 2, Batch 10/63, loss: 758.897↓, reward: 34.804↓, critic_reward: 8.113, revenue_rate: 0.5824, distance: 22.6689, memory: 0.4914, power: 0.8192, lr: 0.000400, took: 97.154s
Batch 10: Reward: 38.7302, Loss: 949.3312, Revenue: 0.6158, LoadBalance: 0.0162, Tasks: [S0:1823(67.4%), S1:8(0.3%), S2:873(32.3%)], ActorGrad: 22351.0371, CriticGrad: 442.5919, Advantage: μ=30.756, σ=1.896, range=[27.43, 35.42]
Batch 15: Reward: 40.3442, Loss: 1040.2263, Revenue: 0.7443, LoadBalance: 0.0000, Tasks: [S0:3140(98.6%), S1:2(0.1%), S2:42(1.3%)], ActorGrad: 32280.5234, CriticGrad: 465.5738, Advantage: μ=32.149, σ=2.664, range=[27.68, 36.88]
Epoch 2, Batch 20/63, loss: 994.245↑, reward: 39.671↑, critic_reward: 8.266, revenue_rate: 0.7136, distance: 25.0910, memory: 0.5451, power: 0.9446, lr: 0.000400, took: 111.767s
Batch 20: Reward: 40.3264, Loss: 1029.9229, Revenue: 0.7970, LoadBalance: 0.0000, Tasks: [S0:3181(99.9%), S1:0(0.0%), S2:3(0.1%)], ActorGrad: 33762.2539, CriticGrad: 457.1745, Advantage: μ=32.009, σ=2.385, range=[26.87, 35.50]
Batch 25: Reward: 40.5152, Loss: 1015.2896, Revenue: 0.8011, LoadBalance: 0.0000, Tasks: [S0:3180(99.9%), S1:1(0.0%), S2:3(0.1%)], ActorGrad: 34607.1914, CriticGrad: 473.3174, Advantage: μ=31.787, σ=2.277, range=[27.08, 35.82]
Epoch 2, Batch 30/63, loss: 1009.332↓, reward: 40.186↑, critic_reward: 8.530, revenue_rate: 0.7984, distance: 26.6070, memory: 0.6425, power: 0.9958, lr: 0.000400, took: 124.261s
Batch 30: Reward: 40.0191, Loss: 1001.8834, Revenue: 0.7898, LoadBalance: 0.0000, Tasks: [S0:3180(99.9%), S1:0(0.0%), S2:4(0.1%)], ActorGrad: 34936.4688, CriticGrad: 454.3049, Advantage: μ=31.516, σ=3.037, range=[27.02, 37.69]
Batch 35: Reward: 40.9211, Loss: 1044.5134, Revenue: 0.7773, LoadBalance: 0.0000, Tasks: [S0:3179(99.8%), S1:2(0.1%), S2:3(0.1%)], ActorGrad: 35657.6211, CriticGrad: 468.0842, Advantage: μ=32.269, σ=1.856, range=[28.62, 34.92]
Epoch 2, Batch 40/63, loss: 1014.877↑, reward: 40.477↑, critic_reward: 8.717, revenue_rate: 0.7815, distance: 25.9260, memory: 0.6434, power: 0.9959, lr: 0.000400, took: 122.066s
Batch 40: Reward: 40.6177, Loss: 1000.7833, Revenue: 0.7931, LoadBalance: 0.0000, Tasks: [S0:3180(99.9%), S1:1(0.0%), S2:3(0.1%)], ActorGrad: 33666.2891, CriticGrad: 477.6583, Advantage: μ=31.507, σ=2.938, range=[26.63, 37.23]
Batch 45: Reward: 40.3985, Loss: 977.3755, Revenue: 0.7713, LoadBalance: 0.0000, Tasks: [S0:3180(99.9%), S1:2(0.1%), S2:2(0.1%)], ActorGrad: 32985.3789, CriticGrad: 473.1388, Advantage: μ=31.180, σ=2.346, range=[27.92, 35.93]
Epoch 2, Batch 50/63, loss: 1013.645↑, reward: 40.710↑, critic_reward: 8.993, revenue_rate: 0.7803, distance: 25.6644, memory: 0.6337, power: 0.9912, lr: 0.000400, took: 123.403s
Batch 50: Reward: 40.0182, Loss: 938.8625, Revenue: 0.7663, LoadBalance: 0.0000, Tasks: [S0:3181(99.9%), S1:1(0.0%), S2:2(0.1%)], ActorGrad: 35423.1602, CriticGrad: 473.4751, Advantage: μ=30.588, σ=1.861, range=[26.85, 34.98]
Batch 55: Reward: 41.5572, Loss: 1051.2302, Revenue: 0.7757, LoadBalance: 0.0000, Tasks: [S0:3178(99.8%), S1:5(0.2%), S2:1(0.0%)], ActorGrad: 36531.6562, CriticGrad: 479.5769, Advantage: μ=32.348, σ=2.277, range=[25.70, 35.36]
Epoch 2, Batch 60/63, loss: 974.422↑, reward: 40.393↑, critic_reward: 9.303, revenue_rate: 0.7786, distance: 25.4900, memory: 0.6582, power: 0.9927, lr: 0.000400, took: 122.904s
Batch 60: Reward: 41.1996, Loss: 1017.6366, Revenue: 0.7750, LoadBalance: 0.0000, Tasks: [S0:3177(99.8%), S1:3(0.1%), S2:4(0.1%)], ActorGrad: 33597.8828, CriticGrad: 477.2869, Advantage: μ=31.819, σ=2.356, range=[26.75, 36.04]

📊 Epoch 2 训练统计:
  平均奖励: 39.4680
  平均损失: 964.3464
  平均收益率: 0.7410
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 35.463, revenue_rate: 0.8574, efficiency: 0.8051, distance: 30.3230, memory: 0.6196, power: 0.9266
Test Batch 1/7, reward: 34.413, revenue_rate: 0.8565, efficiency: 0.7794, distance: 28.8568, memory: 0.6038, power: 0.9191
Test Batch 2/7, reward: 34.045, revenue_rate: 0.8311, efficiency: 0.7394, distance: 28.4222, memory: 0.5019, power: 0.8826
Test Batch 3/7, reward: 35.218, revenue_rate: 0.8445, efficiency: 0.7809, distance: 29.5617, memory: 0.6230, power: 0.9195
Test Batch 4/7, reward: 35.015, revenue_rate: 0.8231, efficiency: 0.7652, distance: 29.7897, memory: 0.6020, power: 0.9374
Test Batch 5/7, reward: 33.434, revenue_rate: 0.8570, efficiency: 0.7838, distance: 29.6913, memory: 0.6020, power: 0.9280
Test Batch 6/7, reward: 32.979, revenue_rate: 0.8552, efficiency: 0.7739, distance: 30.1561, memory: 0.6073, power: 0.9164
Test Summary - Avg reward: 34.533±1.937, revenue_rate: 0.8453±0.0445, efficiency: 0.7756, completion_rate: 0.9174, distance: 29.4694, memory: 0.5927, power: 0.9188
Load Balance - Avg balance score: -0.7265±0.0085
Task Distribution by Satellite:
  Satellite 1: 18217 tasks (99.79%)
  Satellite 2: 20 tasks (0.11%)
  Satellite 3: 19 tasks (0.10%)
✅ 验证完成 - Epoch 2, reward: 34.533, revenue_rate: 0.8453, distance: 29.4694, memory: 0.5927, power: 0.9188
  ⚠️ 过拟合: 训练验证差距 = 4.9347
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_hybrid_2025_08_04_19_49_15 (验证集奖励: 34.5333)

开始训练 Epoch 3/3
Batch 0: Reward: 41.1157, Loss: 984.6394, Revenue: 0.7766, LoadBalance: 0.0000, Tasks: [S0:3173(99.7%), S1:7(0.2%), S2:4(0.1%)], ActorGrad: 32562.2070, CriticGrad: 485.9189, Advantage: μ=31.316, σ=2.060, range=[28.15, 34.73]
Batch 5: Reward: 41.9466, Loss: 1057.9803, Revenue: 0.7713, LoadBalance: 0.0000, Tasks: [S0:3171(99.6%), S1:4(0.1%), S2:9(0.3%)], ActorGrad: 33467.9453, CriticGrad: 485.3823, Advantage: μ=32.417, σ=2.758, range=[28.19, 36.94]
Epoch 3, Batch 10/63, loss: 974.039↑, reward: 40.684↑, critic_reward: 9.623, revenue_rate: 0.7710, distance: 26.1075, memory: 0.6223, power: 0.9937, lr: 0.000400, took: 127.027s
Batch 10: Reward: 40.2578, Loss: 927.1924, Revenue: 0.7522, LoadBalance: 0.0000, Tasks: [S0:3159(99.2%), S1:12(0.4%), S2:13(0.4%)], ActorGrad: 26062.1387, CriticGrad: 468.3842, Advantage: μ=30.352, σ=2.517, range=[26.71, 33.71]
Batch 15: Reward: 40.7355, Loss: 940.2958, Revenue: 0.7392, LoadBalance: 0.0000, Tasks: [S0:3141(98.6%), S1:21(0.7%), S2:22(0.7%)], ActorGrad: 29547.3398, CriticGrad: 481.0636, Advantage: μ=30.629, σ=1.528, range=[27.52, 33.61]
Epoch 3, Batch 20/63, loss: 949.752↑, reward: 40.526↑, critic_reward: 9.813, revenue_rate: 0.7468, distance: 26.4076, memory: 0.6318, power: 0.9955, lr: 0.000400, took: 122.050s
Batch 20: Reward: 40.8782, Loss: 958.0286, Revenue: 0.7733, LoadBalance: 0.0000, Tasks: [S0:3173(99.7%), S1:6(0.2%), S2:5(0.2%)], ActorGrad: 31864.3125, CriticGrad: 472.8772, Advantage: μ=30.882, σ=2.155, range=[28.32, 35.36]
Batch 25: Reward: 40.8776, Loss: 967.0632, Revenue: 0.7772, LoadBalance: 0.0000, Tasks: [S0:3178(99.8%), S1:4(0.1%), S2:2(0.1%)], ActorGrad: 31320.7207, CriticGrad: 465.0815, Advantage: μ=30.976, σ=2.835, range=[25.90, 35.51]
Epoch 3, Batch 30/63, loss: 948.868↓, reward: 40.815↓, critic_reward: 10.137, revenue_rate: 0.7734, distance: 25.4191, memory: 0.6485, power: 0.9989, lr: 0.000400, took: 124.528s
Batch 30: Reward: 41.7296, Loss: 1010.4236, Revenue: 0.7677, LoadBalance: 0.0000, Tasks: [S0:3175(99.7%), S1:2(0.1%), S2:7(0.2%)], ActorGrad: 34532.1641, CriticGrad: 481.0726, Advantage: μ=31.613, σ=3.427, range=[25.75, 37.16]
Batch 35: Reward: 41.4343, Loss: 966.9247, Revenue: 0.7664, LoadBalance: 0.0000, Tasks: [S0:3176(99.7%), S1:1(0.0%), S2:7(0.2%)], ActorGrad: 32191.6250, CriticGrad: 483.3286, Advantage: μ=31.006, σ=2.429, range=[24.62, 34.22]
Epoch 3, Batch 40/63, loss: 956.885↓, reward: 41.239↓, critic_reward: 10.417, revenue_rate: 0.7690, distance: 25.3790, memory: 0.6195, power: 0.9952, lr: 0.000400, took: 122.948s
Batch 40: Reward: 40.7288, Loss: 912.5109, Revenue: 0.7277, LoadBalance: 0.0000, Tasks: [S0:3133(98.4%), S1:10(0.3%), S2:41(1.3%)], ActorGrad: 30260.7637, CriticGrad: 476.4816, Advantage: μ=30.158, σ=1.788, range=[27.46, 33.90]
Batch 45: Reward: 33.9136, Loss: 563.0537, Revenue: 0.5017, LoadBalance: 0.1599, Tasks: [S0:1350(56.6%), S1:64(2.7%), S2:970(40.7%)], ActorGrad: 12071.7773, CriticGrad: 362.0632, Advantage: μ=23.424, σ=3.916, range=[15.82, 31.37]
Epoch 3, Batch 50/63, loss: 631.752↓, reward: 35.142↓, critic_reward: 10.683, revenue_rate: 0.5552, distance: 21.7790, memory: 0.4867, power: 0.8136, lr: 0.000400, took: 92.650s
Batch 50: Reward: 40.1006, Loss: 878.5003, Revenue: 0.7074, LoadBalance: 0.0000, Tasks: [S0:16(0.5%), S1:18(0.6%), S2:3150(98.9%)], ActorGrad: 27774.7168, CriticGrad: 460.8399, Advantage: μ=29.420, σ=3.715, range=[22.79, 35.57]
Batch 55: Reward: 40.9244, Loss: 933.3118, Revenue: 0.7855, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:1(0.0%), S2:3183(100.0%)], ActorGrad: 35016.0664, CriticGrad: 464.9307, Advantage: μ=30.454, σ=2.496, range=[26.55, 36.40]
Epoch 3, Batch 60/63, loss: 901.198↑, reward: 40.651↑, critic_reward: 10.751, revenue_rate: 0.7566, distance: 25.5051, memory: 0.6361, power: 0.9917, lr: 0.000400, took: 122.557s
Batch 60: Reward: 41.7574, Loss: 953.6063, Revenue: 0.7707, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:2(0.1%), S2:3182(99.9%)], ActorGrad: 35921.8633, CriticGrad: 486.3304, Advantage: μ=30.781, σ=2.553, range=[26.42, 36.06]

📊 Epoch 3 训练统计:
  平均奖励: 39.9120
  平均损失: 895.6972
  平均收益率: 0.7303
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/7, reward: 35.240, revenue_rate: 0.8785, efficiency: 0.8299, distance: 30.2623, memory: 0.6219, power: 0.9314
Test Batch 1/7, reward: 35.328, revenue_rate: 0.8566, efficiency: 0.7878, distance: 29.8313, memory: 0.5519, power: 0.9306
Test Batch 2/7, reward: 34.517, revenue_rate: 0.8323, efficiency: 0.7527, distance: 29.1560, memory: 0.6069, power: 0.8938
Test Batch 3/7, reward: 35.171, revenue_rate: 0.8707, efficiency: 0.8141, distance: 30.3611, memory: 0.6159, power: 0.9282
Test Batch 4/7, reward: 34.847, revenue_rate: 0.8713, efficiency: 0.8101, distance: 29.7508, memory: 0.6071, power: 0.9315
Test Batch 5/7, reward: 33.882, revenue_rate: 0.8503, efficiency: 0.7820, distance: 29.7766, memory: 0.5651, power: 0.9321
Test Batch 6/7, reward: 34.188, revenue_rate: 0.8687, efficiency: 0.7992, distance: 30.9449, memory: 0.6089, power: 0.9021
Test Summary - Avg reward: 34.805±2.004, revenue_rate: 0.8603±0.0419, efficiency: 0.7962, completion_rate: 0.9253, distance: 29.8999, memory: 0.5954, power: 0.9237
Load Balance - Avg balance score: -0.7316±0.0024
Task Distribution by Satellite:
  Satellite 1: 1 tasks (0.01%)
  Satellite 2: 2 tasks (0.01%)
  Satellite 3: 18409 tasks (99.98%)
✅ 验证完成 - Epoch 3, reward: 34.805, revenue_rate: 0.8603, distance: 29.8999, memory: 0.5954, power: 0.9237
  ⚠️ 过拟合: 训练验证差距 = 5.1069
已保存新模型到 constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_hybrid_2025_08_04_19_49_15 (验证集奖励: 34.8051)
训练完成

开始测试模型...
Test Batch 0/7, reward: 35.820, revenue_rate: 0.8698, efficiency: 0.8176, distance: 30.5522, memory: 0.5725, power: 0.9131
Test Batch 1/7, reward: 34.699, revenue_rate: 0.8682, efficiency: 0.8075, distance: 30.0643, memory: 0.6496, power: 0.9268
Test Batch 2/7, reward: 34.197, revenue_rate: 0.8524, efficiency: 0.7797, distance: 28.8131, memory: 0.5631, power: 0.9002
Test Batch 3/7, reward: 34.102, revenue_rate: 0.8257, efficiency: 0.7633, distance: 30.4059, memory: 0.6264, power: 0.9339
Test Batch 4/7, reward: 34.877, revenue_rate: 0.8675, efficiency: 0.8196, distance: 30.8226, memory: 0.6101, power: 0.9357
Test Batch 5/7, reward: 36.293, revenue_rate: 0.8697, efficiency: 0.8344, distance: 30.6667, memory: 0.6145, power: 0.9552
Test Batch 6/7, reward: 32.987, revenue_rate: 0.8554, efficiency: 0.7646, distance: 28.6289, memory: 0.6651, power: 0.8686
Test Summary - Avg reward: 34.918±2.188, revenue_rate: 0.8588±0.0440, efficiency: 0.8021, completion_rate: 0.9338, distance: 30.1571, memory: 0.6084, power: 0.9251
Load Balance - Avg balance score: -0.7318±0.0020
Task Distribution by Satellite:
  Satellite 1: 0 tasks (0.00%)
  Satellite 2: 2 tasks (0.01%)
  Satellite 3: 18582 tasks (99.99%)
测试完成 - 平均奖励: 34.918, 平均星座收益率: 0.8588
✅ 模式 HYBRID 训练完成
   保存路径: constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_hybrid_2025_08_04_19_49_15
   平均奖励: 34.9175
   收益率: 0.8588

================================================================================
🎯 多模式训练总结
================================================================================
✅ COOPERATIVE: 奖励=34.8658, 收益率=0.8218
✅ COMPETITIVE: 奖励=34.7630, 收益率=0.8670
✅ HYBRID: 奖励=34.9175, 收益率=0.8588

🏆 最佳模式: HYBRID
   最高奖励: 34.9175
   对应收益率: 0.8588
   模型路径: constellation_smp\constellation_smp200\constellation_gpn_transformerindrnn_hybrid_2025_08_04_19_49_15

🎉 所有模式训练完成！
