"""
Defines the main task for the Constellation SMP (Satellite mission planning).

The Constellation SMP extends the single satellite mission planning to multiple satellites:
    1. Multiple satellites cooperate to complete observation tasks.
    2. Each satellite has its own memory and power constraints.
    3. Satellites can communicate with each other to share task information.
    4. The goal is to maximize the overall revenue of the constellation.

ConstellationSMPDataset:
    static_data: (num_samples, static_size, seq_len)
        StaticData[:, 0, :]: task start time.
        StaticData[:, 1, :]: task longitudinal axis position.
        StaticData[:, 2, :]: task end time.
        StaticData[:, 3, :]: time required for observation tasks.
        StaticData[:, 4, :]: task revenue.
        StaticData[:, 5, :]: task memory consumption.
        StaticData[:, 6, :]: task power consumption.
        StaticData[:, 7, :]: station indicator (optional).
        StaticData[:, 8, :]: satellite id that can access this task (bitmap).

    dynamic_data: (num_samples, dynamic_size, seq_len, num_satellites)
        DynamicData[:, 0, :, :]: task time windows mark per satellite. 1 -> 0.
        DynamicData[:, 1, :, :]: task access mark per satellite. 1 -> 0.
        DynamicData[:, 2, :, :]: memory surplus per satellite. decrement.
        DynamicData[:, 3, :, :]: power surplus per satellite. decrement.
        DynamicData[:, 4, :, :]: task_last per satellite.  Store the ordinal number of the previous node.
        DynamicData[:, 5, :, :]: start time of current task execution per satellite.
        DynamicData[:, 6, :, :]: satellite position (orbit phase).

update_dynamic: To update dynamic element according to chosen_idx and satellite_idx.

update_mask: To update mask according to dynamic, chosen_idx and satellite_idx.

reward: The reward function, objectives of optimization for the entire constellation.

render: Draw out the results of the plan for the constellation.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import torch
from torch.utils.data import Dataset
import matplotlib.cm as cm
import matplotlib as mpl
from matplotlib.font_manager import FontProperties

HAVE_STATION = 1  # 是否含station

# SMP scene parameters
START_TIME_WIDTH = 6.0  # 一次过境总时间
POSITION_WIDTH = 0.5  # 侧摆角度范最大范围 [-0.25,0.25]

END_TIME_START = 0.2
END_TIME_END = 0.3  # 时间窗口长度 [0.2,0.3]

STABILIZATION_TIME = 0.005  # 姿态调整后的稳定时间
REQUIRE_TIME_START = 0.015
REQUIRE_TIME_END = 0.030  # 任务执行所需时间 [0.015,0.030]

REVENUE_START = 1
REVENUE_END = 9  # 收益[1,9]

MEMORY_CONSUME = 0.01  # 内存消耗
POWER_CONSUME = 0.01  # 能量消耗

# Satellite parameters
MOVING_TIME_RATE = 0.1  # 卫星机动时间比率
MOVING_CONSUME_POWER_RATE = 0.01  # 机动能耗比率
ORBITAL_CHARGING_RATE = 0.005  # 在轨充电速率
SOLAR_PANEL_EFFICIENCY = 0.8  # 太阳能板效率
BATTERY_CAPACITY_FACTOR = 1.2  # 电池容量因子
THERMAL_LOSS_RATE = 0.001  # 热损失率

# 时间窗口约束参数
TIME_WINDOW_PENALTY = -2.0  # 时间窗口违反惩罚
EXECUTION_TIME_BUFFER = 0.002  # 执行时间缓冲

# 重新平衡的奖励权重参数 - 提升效率学习能力
REWARD_PROPORTION = 0.15         # 适度提升收益权重，鼓励效率学习
DISTANCE_PROPORTION = -1.5       # 适度降低机动惩罚
POWER_PROPORTION = -0.8          # 适度降低能量惩罚
MEMORY_PROPORTION = -0.8         # 适度降低内存惩罚
TIME_CONSTRAINT_PROPORTION = -4.0  # 适度降低时间约束违反惩罚

# 负载均衡权重 - 重新平衡
LOAD_BALANCE_WEIGHT = 35.0       # 降低负载均衡权重，平衡效率与均衡

# 星座模式特定的奖励权重
COOPERATIVE_WEIGHT = 0.5  # 混合模式中团队奖励的权重
COMPETITIVE_WEIGHT = 0.5  # 混合模式中个体奖励的权重

# Constellation parameters
ORBIT_PHASE_DIFF = 2.0 * np.pi  # 卫星轨道相位差（均匀分布）
COMMUNICATION_RANGE = 0.8  # 卫星间通信范围（归一化）

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
if device == 'cuda':
    torch.backends.cudnn.enabled = False

# 配置中文字体支持
try:
    # 尝试设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
except:
    print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")

# 检查是否有可用的中文字体
def get_available_chinese_font():
    # 常见中文字体列表
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'FangSong', 'KaiTi', 'Arial Unicode MS', 'WenQuanYi Micro Hei']
    available_font = None
    
    for font_name in chinese_fonts:
        try:
            font = FontProperties(fname=mpl.font_manager.findfont(font_name))
            if font is not None:
                available_font = font
                print(f"使用中文字体: {font_name}")
                break
        except:
            continue
    
    return available_font

# 获取可用的中文字体
chinese_font = get_available_chinese_font()

class ConstellationSMPDataset(Dataset):
    def __init__(self, size=50, num_samples=1000000, seed=None, memory_total=0.5, power_total=0.5, num_satellites=3):
        super(ConstellationSMPDataset, self).__init__()
        self.memory_total = memory_total
        self.power_total = power_total
        self.num_satellites = num_satellites
        
        if seed is None:
            seed = np.random.randint(123456789)
        np.random.seed(seed)
        torch.manual_seed(seed)

        static_size = (num_samples, 1, size)
        start_time = torch.from_numpy(np.random.uniform(0, START_TIME_WIDTH, size=static_size)).float()
        position = torch.from_numpy(
            np.random.uniform(POSITION_WIDTH / 2.0 * (-1), POSITION_WIDTH / 2.0, size=static_size)).float()
        end_time = torch.add(start_time, torch.from_numpy(
            np.random.uniform(END_TIME_START, END_TIME_END, size=static_size)).float())
        require_time = torch.from_numpy(
            np.random.uniform(REQUIRE_TIME_START, REQUIRE_TIME_END, size=static_size)).float()
        revenue = torch.from_numpy(np.random.randint(REVENUE_START, REVENUE_END, size=static_size) / 10).float()
        memory_consume = torch.from_numpy(np.random.uniform(0, MEMORY_CONSUME, size=static_size)).float()
        power_consume = torch.from_numpy(np.random.uniform(0, POWER_CONSUME, size=static_size)).float()

        # 为每个任务生成可访问的卫星位图
        # 1表示第一颗卫星可以访问，2表示第二颗卫星可以访问，3表示第一和第二颗卫星都可以访问，以此类推
        satellite_access = torch.zeros(num_samples, 1, size).float()
        
        # 随机生成每个任务可被哪些卫星访问的位图
        for i in range(size):
            for j in range(num_samples):
                # 生成一个随机的位图，确保至少有一颗卫星可以访问每个任务
                bitmap = np.random.randint(1, 2**num_satellites, size=1)[0]
                satellite_access[j, 0, i] = bitmap

        # 处理地面站
        if HAVE_STATION == 1:
            earth_stations = torch.from_numpy(np.random.uniform(0, 1, (num_samples, 1, size))).float()
            station_idx, _ = torch.max(earth_stations, dim=2)
            station_idx = station_idx.expand(num_samples, size).unsqueeze(1)
            station_idx = torch.sub(earth_stations, station_idx).eq(0).int()
            revenue = torch.add(revenue, 3 * station_idx)
            revenue = torch.clamp(revenue, 0.1, 3)
            memory_given = self.memory_total * station_idx
            memory_consume = torch.sub(memory_consume, memory_given)
            
            # 地面站可被所有卫星访问
            satellite_access = satellite_access + station_idx * (2**num_satellites - 1)
            satellite_access = torch.clamp(satellite_access, 0, 2**num_satellites - 1)
            
            self.static = torch.cat((start_time, position, end_time, require_time, revenue,
                                     memory_consume, power_consume, station_idx, satellite_access), dim=1)

        if HAVE_STATION == 0:
            self.static = torch.cat((start_time, position, end_time, require_time, revenue,
                                     memory_consume, power_consume, satellite_access), dim=1)
            
        # 第一个任务设为起始点
        self.static[:, :, 0] = 0.

        # 为每颗卫星创建动态状态
        # 动态状态的形状为 (num_samples, dynamic_features, seq_len, num_satellites)
        dynamic_size = (num_samples, 1, size, num_satellites)
        time_window = torch.from_numpy(np.ones(dynamic_size)).float()
        access = torch.from_numpy(np.ones(dynamic_size)).float()
        memory_surplus = torch.from_numpy(np.ones(dynamic_size) * self.memory_total).float()
        power_surplus = torch.from_numpy(np.ones(dynamic_size) * self.power_total).float()
        last_task = torch.from_numpy(np.zeros(dynamic_size)).float()
        start_execution = torch.from_numpy(np.zeros(dynamic_size)).float()
        
        # 为每颗卫星生成初始轨道相位（均匀分布在[0, 2π)上）
        orbit_phases = torch.zeros(dynamic_size).float()
        for sat_idx in range(num_satellites):
            phase = (ORBIT_PHASE_DIFF / num_satellites) * sat_idx
            orbit_phases[:, :, :, sat_idx] = phase
        
        # 根据卫星访问位图和轨道相位更新访问标记
        for sat_idx in range(num_satellites):
            sat_bit = 2**sat_idx
            for i in range(size):
                # 检查第sat_idx颗卫星是否可以访问任务i
                can_access = (satellite_access[:, 0, i].int() & sat_bit) > 0
                access[:, 0, i, sat_idx] = can_access.float()
        
        self.dynamic = torch.cat((time_window, access, memory_surplus,
                                  power_surplus, last_task, start_execution, orbit_phases), dim=1)

        self.num_nodes = size
        self.size = num_samples

    def __len__(self):
        return self.size

    def __getitem__(self, idx):
        return self.static[idx], self.dynamic[idx], []

    def update_dynamic(self, static, dynamic, chosen_idx, satellite_idx):
        """
        更新动态状态，考虑选定的任务和执行该任务的卫星
        
        static: (batch_size, input_size, seq_len)
        dynamic: (batch_size, input_size, seq_len, num_satellites)
        chosen_idx: (batch_size,) - 选择的任务索引
        satellite_idx: (batch_size,) - 执行任务的卫星索引
        """
        batch_size = dynamic.size(0)
        num_satellites = dynamic.size(3)
        
        # 获取选定卫星的上一个任务
        # 创建索引张量来获取特定卫星的动态数据
        batch_indices = torch.arange(batch_size, device=dynamic.device)
        sat_indices = satellite_idx.to(dynamic.device)
        
        # 获取选定卫星的上一个任务信息
        last_task = dynamic[batch_indices, 4, 0, sat_indices].clone().long().unsqueeze(1)
        last_start_time = dynamic[batch_indices, 5, 0, sat_indices].clone().unsqueeze(1)
        last_position = torch.gather(static[:, 1, :].clone(), 1, last_task)
        last_need_time = torch.gather(static[:, 3, :].clone(), 1, last_task)
        
        # 获取当前选择的任务信息
        chosen_idx = chosen_idx.unsqueeze(1)
        current_start_time_window = torch.gather(static[:, 0, :].clone(), 1, chosen_idx)
        current_position = torch.gather(static[:, 1, :].clone(), 1, chosen_idx)
        current_need_time = torch.gather(static[:, 3, :].clone(), 1, chosen_idx)
        
        # 计算上一个任务的结束时间
        last_end_time = torch.add((MOVING_TIME_RATE * torch.abs(current_position - last_position)),
                                  torch.add(last_start_time, last_need_time))
        
        # 确定当前任务的开始时间
        judge_result = current_start_time_window.ge(last_end_time).float()
        current_start_time = torch.add(torch.mul((1 - judge_result), last_end_time),
                                       torch.mul(judge_result, current_start_time_window))

        # 检查时间窗口约束违反
        task_end_time_window = torch.gather(static[:, 2, :].clone(), 1, chosen_idx)
        task_completion_time = current_start_time + current_need_time + EXECUTION_TIME_BUFFER
        time_window_violation = torch.clamp(task_completion_time - task_end_time_window, min=0.0)
        
        # 计算移动时间和空闲时间以用于功率更新
        moving_time = MOVING_TIME_RATE * torch.abs(current_position - last_position)
        idle_time = current_start_time - last_end_time

        # 更新选定卫星的上一个任务和开始时间
        # 创建新的动态状态副本
        new_dynamic = dynamic.clone()
        
        # 更新选定卫星的上一个任务
        for b in range(batch_size):
            new_dynamic[b, 4, 0, sat_indices[b]] = chosen_idx[b, 0].float()
            new_dynamic[b, 5, 0, sat_indices[b]] = current_start_time[b, 0].float()
        
        # 更新所有卫星的时间窗口
        for sat_idx in range(num_satellites):
            # 获取当前卫星的位置和上一个任务信息
            sat_last_task = dynamic[:, 4, 0, sat_idx].clone().long().unsqueeze(1)
            sat_last_start_time = dynamic[:, 5, 0, sat_idx].clone().unsqueeze(1)
            sat_last_position = torch.gather(static[:, 1, :].clone(), 1, sat_last_task)
            sat_last_need_time = torch.gather(static[:, 3, :].clone(), 1, sat_last_task)
            
            # 计算当前卫星的上一个任务结束时间
            sat_last_end_time = torch.add((MOVING_TIME_RATE * torch.abs(current_position - sat_last_position)),
                                      torch.add(sat_last_start_time, sat_last_need_time))
            
            # 更新时间窗口
            next_position = static[:, 1, :].clone()
            next_need_time = static[:, 3, :].clone()
            next_end_time = static[:, 2, :].clone()
            
            satisfy_time = torch.add(torch.add((MOVING_TIME_RATE * torch.abs(next_position - sat_last_position)),
                                           torch.add(sat_last_end_time, sat_last_need_time)), next_need_time)
            
            satisfy_time_window = next_end_time.ge(satisfy_time)
            have_time_window = dynamic[:, 0, :, sat_idx].clone().sum(1).gt(0).unsqueeze(1)
            time_window = torch.mul(satisfy_time_window, have_time_window).float()
            
            # 更新访问标记
            access = dynamic[:, 1, :, sat_idx].clone()
            
            # 如果是当前选定的卫星和任务，将访问标记设为0
            for b in range(batch_size):
                if sat_idx == sat_indices[b]:
                    access[b, chosen_idx[b, 0]] = 0
            
            # 修复：正确的资源更新逻辑
            # 逐个批次元素处理卫星索引比较
            for b in range(batch_size):
                if sat_idx == sat_indices[b].item():
                    # 获取当前执行任务的资源需求
                    task_idx = chosen_idx[b, 0].item()
                    memory_consume = static[b, 5, task_idx].clone()
                    task_power_consume = static[b, 6, task_idx].clone()

                    # 只更新执行任务的资源，而不是所有任务
                    current_memory = dynamic[b, 2, :, sat_idx].clone()
                    current_power = dynamic[b, 3, :, sat_idx].clone()

                    # 内存更新：只扣除当前任务的内存消耗
                    new_memory = current_memory.clone()
                    new_memory[task_idx] = torch.clamp(
                        current_memory[task_idx] - memory_consume,
                        0.0,
                        self.memory_total
                    )
                    new_dynamic[b, 2, :, sat_idx] = new_memory

                    # 能量更新：考虑任务消耗、机动消耗和充电收益
                    # 计算机动能耗
                    moving_power_cost = moving_time[b] * MOVING_CONSUME_POWER_RATE

                    # 计算充电收益
                    base_charging = idle_time[b] * ORBITAL_CHARGING_RATE * SOLAR_PANEL_EFFICIENCY
                    thermal_loss = idle_time[b] * THERMAL_LOSS_RATE
                    power_gained = torch.clamp(base_charging - thermal_loss, min=0.0)

                    # 总能量变化
                    total_power_change = power_gained - moving_power_cost - task_power_consume
                    max_capacity = self.power_total * BATTERY_CAPACITY_FACTOR

                    # 更新所有任务位置的能量（因为充电和机动影响整个卫星）
                    new_power = torch.clamp(
                        current_power + total_power_change,
                        0.0,
                        max_capacity
                    )
                    new_dynamic[b, 3, :, sat_idx] = new_power
            
            # 更新时间窗口和访问标记
            new_dynamic[:, 0, :, sat_idx] = time_window
            new_dynamic[:, 1, :, sat_idx] = access
        
        # 修复：根据任务类型和星座模式调整任务共享策略
        # 对于大多数观测任务，一旦被执行就不能再被其他卫星执行
        # 但保留灵活性以支持未来的协同任务类型

        # 当前实现：所有任务都是独占的（观测任务的典型特征）
        for sat_idx in range(num_satellites):
            for b in range(batch_size):
                task_idx = chosen_idx[b, 0].item()
                # 将已执行的任务标记为不可访问
                new_dynamic[b, 1, task_idx, sat_idx] = 0
        
        return new_dynamic

    def update_mask(self, dynamic, satellite_idx=None, satellite_loads=None, static=None):
        """
        更新掩码，基于最优卫星的资源约束（方案2实施）

        dynamic: (batch_size, input_size, seq_len, num_satellites)
        satellite_idx: (batch_size,) - 如果提供，则只更新特定卫星的掩码
        satellite_loads: (batch_size, num_satellites) - 每颗卫星的当前负载
        static: (batch_size, static_size, seq_len) - 静态任务特征，用于获取资源需求

        返回：
        mask: (batch_size, seq_len) - 基于最优卫星的任务掩码
        satellite_masks: (batch_size, seq_len, num_satellites) - 每颗卫星的单独掩码
        """
        batch_size = dynamic.size(0)
        seq_len = dynamic.size(2)
        num_satellites = dynamic.size(3)

        # 创建每颗卫星的掩码
        satellite_masks = torch.zeros(batch_size, seq_len, num_satellites, device=dynamic.device)

        # 对每颗卫星计算掩码
        for sat_idx in range(num_satellites):
            # 时间窗口约束
            time_window_mask = dynamic[:, 0, :, sat_idx].clone()

            # 访问约束
            access_mask = dynamic[:, 1, :, sat_idx].clone()

            # 修复：正确的资源约束检查
            if static is not None:
                # 获取任务的资源需求
                task_memory_need = static[:, 5, :].clone()  # 任务内存需求
                task_power_need = static[:, 6, :].clone()   # 任务能量需求

                # 获取卫星的剩余资源
                satellite_memory_surplus = dynamic[:, 2, :, sat_idx].clone()
                satellite_power_surplus = dynamic[:, 3, :, sat_idx].clone()

                # 内存约束：剩余内存 >= 任务需求内存
                memory_mask = satellite_memory_surplus.ge(task_memory_need).float()

                # 能量约束：剩余能量 >= 任务需求能量
                power_mask = satellite_power_surplus.ge(task_power_need).float()
            else:
                # 如果没有static信息，使用简化的资源检查（向后兼容）
                memory_mask = dynamic[:, 2, :, sat_idx].clone().gt(0).float()
                power_mask = dynamic[:, 3, :, sat_idx].clone().gt(0).float()

            # 组合所有约束
            sat_mask = time_window_mask * access_mask * memory_mask * power_mask
            satellite_masks[:, :, sat_idx] = sat_mask

        # 如果指定了卫星索引，则只返回该卫星的掩码
        if satellite_idx is not None:
            batch_indices = torch.arange(batch_size, device=dynamic.device)
            return satellite_masks[batch_indices, :, satellite_idx]

        # 修复：一致的任务选择逻辑
        if satellite_loads is not None:
            # 计算加权掩码，优先考虑负载轻的卫星
            # 负载权重：负载越小权重越大
            load_weights = torch.softmax(-satellite_loads * 2.0, dim=1)  # 温度参数2.0增强差异

            # 加权平均所有卫星的掩码
            weighted_masks = satellite_masks * load_weights.unsqueeze(1).unsqueeze(1)
            mask = torch.sum(weighted_masks, dim=2)

            # 阈值化：只有加权分数超过阈值的任务才被认为可执行
            threshold = 0.3  # 可调参数
            mask = (mask >= threshold).float()

            # 如果没有任务可执行，降低阈值重试
            for b in range(batch_size):
                if torch.sum(mask[b]) == 0:
                    # 逐步降低阈值
                    for thresh in [0.2, 0.1, 0.05]:
                        temp_mask = (torch.sum(weighted_masks[b], dim=1) >= thresh).float()
                        if torch.sum(temp_mask) > 0:
                            mask[b] = temp_mask
                            break

                    # 最后的回退：使用任何卫星能执行的任务
                    if torch.sum(mask[b]) == 0:
                        mask[b] = torch.max(satellite_masks[b], dim=1)[0]
        else:
            # 如果没有提供负载信息，使用综合掩码
            mask = torch.max(satellite_masks, dim=2)[0]

        return mask, satellite_masks


def reward(static, tour_indices, satellite_indices, constellation_mode='cooperative'):
    """
    计算星座任务规划的奖励
    
    static: (batch_size, static_size, seq_len)
    tour_indices: (batch_size, tour_len)
    satellite_indices: (batch_size, tour_len)
    constellation_mode: 'cooperative', 'competitive', or 'hybrid'
    
    返回：
    reward: (batch_size,) - 总奖励
    revenue_rate: (batch_size,) - 收益率
    distance: (batch_size,) - 总距离
    memory: (batch_size,) - 内存使用
    power: (batch_size,) - 能量使用
    """
    batch_size = static.size(0)
    
    # 初始化每颗卫星的统计信息
    num_satellites = torch.max(satellite_indices).item() + 1
    satellite_revenues = torch.zeros(batch_size, num_satellites, device=static.device)
    satellite_distances = torch.zeros(batch_size, num_satellites, device=static.device)
    satellite_memories = torch.zeros(batch_size, num_satellites, device=static.device)
    satellite_powers = torch.zeros(batch_size, num_satellites, device=static.device)
    satellite_time_violations = torch.zeros(batch_size, num_satellites, device=static.device)
    
    # 计算每颗卫星的贡献
    for i in range(1, tour_indices.size(1)):
        # 获取当前任务和卫星
        prev_idx = tour_indices[:, i-1]
        curr_idx = tour_indices[:, i]
        sat_idx = satellite_indices[:, i]
        
        # 计算收益
        revenue = torch.gather(static[:, 4, :], 1, curr_idx.unsqueeze(1)).squeeze(1)
        
        # 打印当前任务的索引和收益
        # print(f"任务索引: {curr_idx.cpu().numpy()}, 单任务收益: {revenue.cpu().numpy()}")
        
        # 计算距离
        prev_pos = torch.gather(static[:, 1, :], 1, prev_idx.unsqueeze(1)).squeeze(1)
        curr_pos = torch.gather(static[:, 1, :], 1, curr_idx.unsqueeze(1)).squeeze(1)
        distance = torch.abs(curr_pos - prev_pos)
        
        # 计算内存使用
        memory = torch.gather(static[:, 5, :], 1, curr_idx.unsqueeze(1)).squeeze(1)
        
        # 计算能量使用
        power = torch.gather(static[:, 6, :], 1, curr_idx.unsqueeze(1)).squeeze(1)

        # 计算时间约束违反（简化版本，实际应该从动态状态中获取）
        task_start_time = torch.gather(static[:, 0, :], 1, curr_idx.unsqueeze(1)).squeeze(1)
        task_end_time = torch.gather(static[:, 2, :], 1, curr_idx.unsqueeze(1)).squeeze(1)
        task_duration = torch.gather(static[:, 3, :], 1, curr_idx.unsqueeze(1)).squeeze(1)

        # 简化的时间违反计算（实际执行时间超出时间窗口）
        time_violation = torch.clamp(task_start_time + task_duration - task_end_time, min=0.0)

        # 更新每颗卫星的统计信息
        for b in range(batch_size):
            s_idx = sat_idx[b].item()
            satellite_revenues[b, s_idx] += revenue[b]
            satellite_distances[b, s_idx] += distance[b]
            satellite_memories[b, s_idx] += memory[b]
            satellite_powers[b, s_idx] += power[b]
            satellite_time_violations[b, s_idx] += time_violation[b]
    
    # 重新设计的负载均衡奖励计算
    def calculate_load_balance_reward(satellite_task_counts):
        """
        计算负载均衡奖励 - 强化版本
        使用多重惩罚机制确保任务均匀分配
        """
        total_tasks = torch.sum(satellite_task_counts.float(), dim=1)

        # 方法1: 基于标准差的惩罚（原有方法改进）
        mean_tasks = total_tasks / num_satellites
        std_dev = torch.std(satellite_task_counts.float(), dim=1)
        max_possible_std = torch.sqrt((total_tasks ** 2 * (num_satellites - 1)) / num_satellites)
        normalized_std = std_dev / (max_possible_std + 1e-8)
        std_penalty = -normalized_std * 10.0  # 标准差惩罚

        # 方法2: 单卫星主导惩罚（新增）
        max_tasks_per_satellite = torch.max(satellite_task_counts.float(), dim=1)[0]
        dominance_ratio = max_tasks_per_satellite / (total_tasks + 1e-8)
        # 当单卫星承担超过50%任务时，给予严厉惩罚
        dominance_penalty = torch.where(
            dominance_ratio > 0.5,
            -100.0 * (dominance_ratio - 0.5) ** 2,  # 二次惩罚
            torch.zeros_like(dominance_ratio)
        )

        # 方法3: 空闲卫星惩罚（新增）
        idle_satellites = torch.sum((satellite_task_counts == 0).float(), dim=1)
        idle_penalty = -idle_satellites * 20.0  # 每个空闲卫星惩罚20分

        # 方法4: 理想分配奖励（新增）
        ideal_tasks_per_satellite = total_tasks / num_satellites
        deviation_from_ideal = torch.abs(satellite_task_counts.float() - ideal_tasks_per_satellite.unsqueeze(1))
        total_deviation = torch.sum(deviation_from_ideal, dim=1)
        ideal_bonus = torch.where(
            total_deviation < ideal_tasks_per_satellite * 0.3,  # 偏差小于30%
            10.0 * (1.0 - total_deviation / (ideal_tasks_per_satellite + 1e-8)),
            torch.zeros_like(total_deviation)
        )

        # 综合负载均衡分数
        balance_reward = std_penalty + dominance_penalty + idle_penalty + ideal_bonus

        return balance_reward

    # 计算每颗卫星的任务数量
    satellite_task_counts = torch.zeros(batch_size, num_satellites, device=static.device)
    for b in range(batch_size):
        # 获取当前批次的卫星索引序列
        sat_seq = satellite_indices[b]
        seq_len = sat_seq.size(0) if hasattr(sat_seq, 'size') else len(sat_seq)

        for i in range(seq_len):
            if i > 0:  # 跳过起始点
                sat_idx = sat_seq[i].item() if hasattr(sat_seq[i], 'item') else int(sat_seq[i])
                if 0 <= sat_idx < num_satellites:
                    satellite_task_counts[b, sat_idx] += 1

    load_balance_reward = calculate_load_balance_reward(satellite_task_counts)

    # 根据星座模式计算奖励 - 重新设计版本
    if constellation_mode == 'cooperative':
        # 协同模式：平衡收益与负载均衡
        total_revenue = torch.sum(satellite_revenues, dim=1)
        total_distance = torch.sum(satellite_distances, dim=1)
        total_memory = torch.sum(satellite_memories, dim=1)
        total_power = torch.sum(satellite_powers, dim=1)
        total_time_violations = torch.sum(satellite_time_violations, dim=1)

        # 新的奖励计算：不再使用revenue_scale，直接使用降低的权重
        # 这样可以保持奖励的相对关系，同时控制数值大小
        reward = REWARD_PROPORTION * total_revenue + \
                 DISTANCE_PROPORTION * total_distance + \
                 MEMORY_PROPORTION * total_memory + \
                 POWER_PROPORTION * total_power + \
                 TIME_CONSTRAINT_PROPORTION * total_time_violations + \
                 LOAD_BALANCE_WEIGHT * load_balance_reward
                 
    elif constellation_mode == 'competitive':
        # 竞争模式：在竞争中也要考虑负载均衡
        # 虽然卫星竞争，但系统整体仍需要均衡
        total_revenue = torch.sum(satellite_revenues, dim=1)
        total_distance = torch.sum(satellite_distances, dim=1)
        total_memory = torch.sum(satellite_memories, dim=1)
        total_power = torch.sum(satellite_powers, dim=1)
        total_time_violations = torch.sum(satellite_time_violations, dim=1)

        # 竞争模式下适度降低负载均衡权重，增强效率导向
        competitive_balance_weight = LOAD_BALANCE_WEIGHT * 0.6

        reward = REWARD_PROPORTION * total_revenue + \
                 DISTANCE_PROPORTION * total_distance + \
                 MEMORY_PROPORTION * total_memory + \
                 POWER_PROPORTION * total_power + \
                 TIME_CONSTRAINT_PROPORTION * total_time_violations + \
                 competitive_balance_weight * load_balance_reward
                 
    elif constellation_mode == 'hybrid':
        # 混合模式：平衡个体竞争与团队协作
        # 个体奖励部分
        individual_rewards = REWARD_PROPORTION * satellite_revenues + \
                             DISTANCE_PROPORTION * satellite_distances + \
                             MEMORY_PROPORTION * satellite_memories + \
                             POWER_PROPORTION * satellite_powers + \
                             TIME_CONSTRAINT_PROPORTION * satellite_time_violations

        # 团队奖励部分
        total_revenue = torch.sum(satellite_revenues, dim=1)
        total_distance = torch.sum(satellite_distances, dim=1)
        total_memory = torch.sum(satellite_memories, dim=1)
        total_power = torch.sum(satellite_powers, dim=1)
        total_time_violations = torch.sum(satellite_time_violations, dim=1)

        team_reward = REWARD_PROPORTION * total_revenue + \
                      DISTANCE_PROPORTION * total_distance + \
                      MEMORY_PROPORTION * total_memory + \
                      POWER_PROPORTION * total_power + \
                      TIME_CONSTRAINT_PROPORTION * total_time_violations

        # 混合模式下负载均衡权重适中，平衡协作与效率
        hybrid_balance_weight = LOAD_BALANCE_WEIGHT * 1.0

        # 最终奖励：个体+团队+强化负载均衡
        reward = COMPETITIVE_WEIGHT * individual_rewards.sum(dim=1) + \
                 COOPERATIVE_WEIGHT * team_reward + \
                 hybrid_balance_weight * load_balance_reward
    else:
        raise ValueError(f"未知的星座模式: {constellation_mode}")

    # 计算总体统计信息，用于日志记录和分析
    final_total_revenue = torch.sum(satellite_revenues, dim=1)
    final_total_distance = torch.sum(satellite_distances, dim=1)
    final_total_memory = torch.sum(satellite_memories, dim=1)
    final_total_power = torch.sum(satellite_powers, dim=1)
    final_total_time_violations = torch.sum(satellite_time_violations, dim=1)

    # 计算所有可用任务的总收益（与单星任务保持一致）
    all_possible_revenue = torch.sum(static[:, 4, :], dim=1)

    # 计算收益率（修复多卫星重复计算问题）
    # 在多卫星系统中，我们需要计算实际完成的唯一任务的收益
    # 而不是所有卫星收益的简单求和

    # 计算实际完成的唯一任务收益
    unique_completed_revenue = torch.zeros_like(final_total_revenue)
    for b in range(batch_size):
        completed_tasks = set()
        batch_revenue = 0.0

        # 遍历该批次中的所有任务
        for i in range(len(tour_indices[b])):
            task_idx = tour_indices[b][i].item()
            if task_idx not in completed_tasks and task_idx > 0:  # 排除填充的0
                completed_tasks.add(task_idx)
                batch_revenue += static[b, 4, task_idx].item()  # 任务收益在static的第4维

        unique_completed_revenue[b] = batch_revenue

    # 对时间违反进行惩罚调整
    adjusted_revenue = unique_completed_revenue - final_total_time_violations * 0.1
    revenue_rate = torch.clamp(adjusted_revenue, min=0.0) / (all_possible_revenue + 1e-10)

    return reward, revenue_rate, final_total_distance, final_total_memory, final_total_power


def render(static, tour_indices, satellite_indices, save_path, num_satellites, num=0):
    """
    可视化星座任务规划结果
    """
    plt.close('all')
    
    batch_idx = num
    seq_len = static.size(2)
    
    fig = plt.figure(figsize=(15, 10))
    
    # 1. 收集所有已执行的任务ID
    executed_tasks_set = set()
    if tour_indices.numel() > 0:
        executed_tasks_set.update(tour_indices[batch_idx][tour_indices[batch_idx] > 0].cpu().numpy())

    # 2. 绘制所有未执行的任务点和时间窗口
    #    为图例创建占位符
    plt.scatter([], [], c='lightgray', s=30, label='Unexecuted Task')
    if HAVE_STATION == 1 and static.size(1) > 7:
        plt.scatter([], [], c='orange', marker='^', s=50, label='Unexecuted Station')

    for i in range(1, seq_len):
        if i in executed_tasks_set:
            continue
        
        start_time = static[batch_idx, 0, i].cpu().item()
        end_time = static[batch_idx, 2, i].cpu().item()
        position = static[batch_idx, 1, i].cpu().item()
        is_station = HAVE_STATION == 1 and static.size(1) > 7 and static[batch_idx, 7, i] == 1
        
        if is_station:
            plt.scatter(start_time, position, c='orange', marker='^', s=50, alpha=0.8)
        else:
            plt.scatter(start_time, position, c='lightgray', s=30, alpha=0.6)
        
        plt.hlines(y=position, xmin=start_time, xmax=end_time, colors='gray', linestyles='--', alpha=0.5)

    # 3. 绘制每颗卫星的轨迹和已执行的任务
    colors = cm.rainbow(np.linspace(0, 1, num_satellites))
    
    for sat_idx in range(num_satellites):
        sat_mask = (satellite_indices[batch_idx] == sat_idx)
        if not torch.any(sat_mask):
            continue
            
        sat_tour = tour_indices[batch_idx][sat_mask]
        sat_tour = sat_tour[sat_tour > 0]
        if len(sat_tour) == 0:
            continue

        power = torch.gather(static[batch_idx, 6, :], 0, sat_tour).sum()
        task_start_times = torch.gather(static[batch_idx, 0, :], 0, sat_tour)
        task_positions = torch.gather(static[batch_idx, 1, :], 0, sat_tour)
        task_require_times = torch.gather(static[batch_idx, 3, :], 0, sat_tour)
        
        exec_times = []
        last_pos = static[batch_idx, 1, 0]
        last_exec_end_time = 0.0
        for i in range(len(sat_tour)):
            move_time = MOVING_TIME_RATE * torch.abs(task_positions[i] - last_pos)
            arrival_time = last_exec_end_time + move_time
            exec_start_time = torch.max(arrival_time, task_start_times[i])
            exec_times.append(exec_start_time.cpu().item())
            last_exec_end_time = exec_start_time + task_require_times[i]
            last_pos = task_positions[i]

        x_coords = np.array(exec_times)
        y_coords = task_positions.cpu().numpy()

        plt.plot(x_coords, y_coords, '-', c=colors[sat_idx], 
                 label=f'Satellite {sat_idx+1} (Power: {power:.2f})', linewidth=1.5, zorder=2)
        
        for i, task_idx in enumerate(sat_tour):
            is_station = HAVE_STATION == 1 and static.size(1) > 7 and static[batch_idx, 7, task_idx] == 1
            if is_station:
                plt.scatter(x_coords[i], y_coords[i], color=colors[sat_idx], marker='*', s=300, 
                            label='Executed Station', zorder=3, edgecolors='black')
            else:
                plt.scatter(x_coords[i], y_coords[i], color=colors[sat_idx], marker='o', s=60, zorder=3)
            plt.annotate(f'{task_idx.item()}', (x_coords[i], y_coords[i]), fontsize=8, ha='right')

        if len(x_coords) > 0:
            plt.annotate(f'Start: {sat_tour[0].item()}', (x_coords[0], y_coords[0]), textcoords="offset points", xytext=(0,10), ha='center', arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=.2"), bbox=dict(boxstyle="round,pad=0.3", fc=colors[sat_idx], alpha=0.5))
            if len(x_coords) > 1:
                plt.annotate(f'End: {sat_tour[-1].item()}', (x_coords[-1], y_coords[-1]), textcoords="offset points", xytext=(0,-15), ha='center', arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=-.2"), bbox=dict(boxstyle="round,pad=0.3", fc=colors[sat_idx], alpha=0.5))

    # 4. 创建唯一的图例
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    # 为Executed Station添加一个虚拟的图例项
    if 'Executed Station' not in by_label:
        by_label['Executed Station'] = plt.Line2D([0], [0], marker='*', color='w', label='Executed Station', markerfacecolor='gold', markersize=15)

    plt.legend(by_label.values(), by_label.keys(), loc='upper right', prop=chinese_font)

    # 计算并显示任务分配统计信息
    satellite_task_counts = [0] * num_satellites
    total_tasks = 0

    for sat_idx in range(num_satellites):
        sat_mask = (satellite_indices[batch_idx] == sat_idx)
        # 排除起始点（索引0）
        valid_tasks = sat_mask[1:] if len(sat_mask) > 1 else sat_mask
        satellite_task_counts[sat_idx] = valid_tasks.sum().item()
        total_tasks += satellite_task_counts[sat_idx]

    # 计算负载均衡分数
    if total_tasks > 0:
        task_counts_tensor = torch.tensor(satellite_task_counts, dtype=torch.float)
        mean_tasks = task_counts_tensor.mean()
        std_dev = task_counts_tensor.std()
        balance_score = 1.0 - (std_dev / (mean_tasks + 1e-8))

        # 在图上添加统计信息
        stats_text = f'任务分配统计:\n'
        for sat_idx in range(num_satellites):
            percentage = (satellite_task_counts[sat_idx] / total_tasks * 100) if total_tasks > 0 else 0
            stats_text += f'卫星{sat_idx+1}: {satellite_task_counts[sat_idx]}个任务 ({percentage:.1f}%)\n'
        stats_text += f'负载均衡分数: {balance_score:.3f}'

        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                fontproperties=chinese_font, fontsize=9, verticalalignment='top',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))

    plt.xlabel('时间', fontproperties=chinese_font)
    plt.ylabel('位置', fontproperties=chinese_font)
    plt.title('卫星星座任务规划结果', fontproperties=chinese_font)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    if not os.path.exists(os.path.dirname(save_path)):
        os.makedirs(os.path.dirname(save_path))
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()