🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp200\multi_mode_training_2025_08_05_10_17_49
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 200
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 16
seed: 12346
train-size: 1000
valid-size: 100
epochs: 2
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_05_10_17_56
使用模型: gpn_transformer
Actor参数数量: 5,929,732
Critic参数数量: 2,942,081

开始训练 Epoch 1/2
Batch 0: Reward: 22.0687, Loss: 39523.1641, Revenue: 0.1743, LoadBalance: 0.6892, Tasks: [S0:221(31.4%), S1:224(31.8%), S2:259(36.8%)], ActorGrad: 5462.0347, CriticGrad: 4317.7456, Advantage: μ=1.088, σ=10.000, range=[-7.87, 18.77]
Batch 5: Reward: -1325.6624, Loss: 1756392.2500, Revenue: 0.5466, LoadBalance: 0.0000, Tasks: [S0:86(3.7%), S1:53(2.3%), S2:2165(94.0%)], ActorGrad: 841698.3125, CriticGrad: 33862.9531, Advantage: μ=-1323.288, σ=75.191, range=[-1451.53, -1147.72]
Epoch 1, Batch 10/63, loss: 880269.271↑, reward: -756.372↓, critic_reward: -1.624, revenue_rate: 0.4389, distance: 16.8537, memory: 0.3507, power: 0.5767, lr: 0.000400, took: 61.947s
Batch 10: Reward: -644.6966, Loss: 501448.2500, Revenue: 0.5597, LoadBalance: 0.0000, Tasks: [S0:1916(74.8%), S1:68(2.7%), S2:576(22.5%)], ActorGrad: 13293.5527, CriticGrad: 10379.8574, Advantage: μ=-20.560, σ=10.000, range=[-57.07, -13.42]
Batch 15: Reward: -2038.7378, Loss: 4431694.0000, Revenue: 0.6992, LoadBalance: 0.0000, Tasks: [S0:2961(97.9%), S1:47(1.6%), S2:16(0.5%)], ActorGrad: 29806.2012, CriticGrad: 28658.2207, Advantage: μ=-36.326, σ=10.000, range=[-46.84, -23.98]
Epoch 1, Batch 20/63, loss: 2998778.875↑, reward: -1623.927↓, critic_reward: -4.600, revenue_rate: 0.6484, distance: 25.5659, memory: 0.5806, power: 0.8848, lr: 0.000400, took: 116.782s
Batch 20: Reward: -2141.0229, Loss: 4968902.0000, Revenue: 0.7415, LoadBalance: 0.0000, Tasks: [S0:3141(98.6%), S1:18(0.6%), S2:25(0.8%)], ActorGrad: 25929.3691, CriticGrad: 29966.6758, Advantage: μ=-32.432, σ=10.000, range=[-55.35, -22.56]
Batch 25: Reward: -1418.7351, Loss: 2001955.7500, Revenue: 0.5745, LoadBalance: 0.0000, Tasks: [S0:2498(95.8%), S1:68(2.6%), S2:42(1.6%)], ActorGrad: 908793.6250, CriticGrad: 20510.8047, Advantage: μ=-1413.146, σ=72.833, range=[-1503.44, -1260.47]
Epoch 1, Batch 30/63, loss: 3061076.038↓, reward: -1676.888↑, critic_reward: -5.344, revenue_rate: 0.6274, distance: 24.7529, memory: 0.5002, power: 0.8798, lr: 0.000400, took: 104.864s
Batch 30: Reward: -1372.1544, Loss: 1949306.0000, Revenue: 0.5158, LoadBalance: 0.0000, Tasks: [S0:2321(93.6%), S1:35(1.4%), S2:124(5.0%)], ActorGrad: 27181.0586, CriticGrad: 18971.5879, Advantage: μ=-46.395, σ=10.000, range=[-82.59, -38.93]
