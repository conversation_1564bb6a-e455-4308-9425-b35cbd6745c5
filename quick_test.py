"""
快速测试脚本
验证改进后的星座任务规划模型是否正常工作
"""
import torch
import numpy as np
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward
from constellation_smp.model_factory import ModelFactory, ModelManager
from constellation_smp.config_manager import ConfigManager

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


def test_physical_constraints():
    """测试物理约束模型"""
    print("=" * 50)
    print("测试1: 物理约束模型")
    print("=" * 50)
    
    # 创建测试数据
    dataset = ConstellationSMPDataset(
        size=20,
        num_samples=10,
        num_satellites=3,
        memory_total=0.3,
        power_total=5.0
    )
    
    # 获取一个样本
    static, dynamic, _ = dataset[0]
    print(f"静态特征形状: {static.shape}")
    print(f"动态特征形状: {dynamic.shape}")
    
    # 检查能量和内存约束
    print(f"初始能量状态: {dynamic[4, :, :].numpy()}")  # power_surplus
    print(f"初始内存状态: {dynamic[6, :, :].numpy()}")  # memory_surplus
    
    # 创建简单的任务序列进行测试
    batch_size = 1
    static_batch = static.unsqueeze(0)
    dynamic_batch = dynamic.unsqueeze(0)
    
    # 模拟任务选择
    tour_indices = torch.tensor([[0, 5, 10, 15]], dtype=torch.long)
    satellite_indices = torch.tensor([[0, 1, 2, 0]], dtype=torch.long)
    
    # 计算奖励
    R, revenue_rate, distance, memory, power = reward(static_batch, tour_indices, satellite_indices, 'competitive')
    print(f"奖励值: {R.item():.4f}")
    print(f"收益率: {revenue_rate.item():.4f}")
    print(f"距离: {distance.item():.4f}")
    print(f"内存: {memory.item():.4f}")
    print(f"能量: {power.item():.4f}")
    
    print("✓ 物理约束模型测试通过")


def test_transformer_model():
    """测试Transformer模型"""
    print("\n" + "=" * 50)
    print("测试2: Transformer模型")
    print("=" * 50)
    
    try:
        # 创建配置
        config_manager = ConfigManager()
        config_manager.model_config.model_type = 'gpn_transformer'
        config_manager.model_config.num_satellites = 3
        config_manager.model_config.d_model = 128  # 使用较小的模型进行测试
        config_manager.model_config.n_transformer_layers = 2
        
        # 创建测试数据
        dataset = ConstellationSMPDataset(
            size=10,
            num_samples=5,
            num_satellites=3
        )
        
        # 创建args对象
        class Args:
            pass
        
        args = Args()
        for key, value in config_manager.get_model_params().items():
            setattr(args, key, value)
        for key, value in config_manager.get_training_params().items():
            setattr(args, key, value)
        
        # 创建模型
        model_manager = ModelManager('gpn_transformer', args, dataset)
        actor = model_manager.actor
        critic = model_manager.critic
        
        print(f"Actor参数数量: {sum(p.numel() for p in actor.parameters()):,}")
        print(f"Critic参数数量: {sum(p.numel() for p in critic.parameters()):,}")
        
        # 测试前向传播
        static, dynamic, _ = dataset[0]
        static_batch = static.unsqueeze(0).to(device)
        dynamic_batch = dynamic.unsqueeze(0).to(device)
        
        actor.eval()
        critic.eval()
        
        with torch.no_grad():
            # 测试Actor
            tour_indices, satellite_indices, tour_logp = actor(static_batch, dynamic_batch)
            print(f"任务序列形状: {tour_indices.shape}")
            print(f"卫星选择形状: {satellite_indices.shape}")
            print(f"对数概率形状: {tour_logp.shape}")
            
            # 测试Critic
            value = critic(static_batch, dynamic_batch)
            print(f"状态价值: {value.item():.4f}")
        
        print("✓ Transformer模型测试通过")
        
    except Exception as e:
        print(f"✗ Transformer模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_performance_metrics():
    """测试性能指标计算"""
    print("\n" + "=" * 50)
    print("测试3: 性能指标计算")
    print("=" * 50)
    
    # 创建测试数据
    dataset = ConstellationSMPDataset(
        size=15,
        num_samples=5,
        num_satellites=3
    )
    
    # 模拟多个批次的结果
    batch_rewards = []
    batch_revenue_rates = []
    
    for i in range(3):
        static, dynamic, _ = dataset[i]
        static_batch = static.unsqueeze(0)
        dynamic_batch = dynamic.unsqueeze(0)
        
        # 模拟任务选择
        tour_indices = torch.randint(0, 15, (1, 10))
        satellite_indices = torch.randint(0, 3, (1, 10))
        
        # 计算奖励
        R, revenue_rate, _, _, _ = reward(static_batch, tour_indices, satellite_indices, 'hybrid')
        batch_rewards.append(R.item())
        
        # 使用从reward函数返回的收益率
        batch_revenue_rates.append(revenue_rate.item())
    
    # 计算统计指标
    avg_reward = np.mean(batch_rewards)
    std_reward = np.std(batch_rewards)
    avg_revenue_rate = np.mean(batch_revenue_rates)
    std_revenue_rate = np.std(batch_revenue_rates)
    
    print(f"平均奖励: {avg_reward:.4f} ± {std_reward:.4f}")
    print(f"平均收益率: {avg_revenue_rate:.4f} ± {std_revenue_rate:.4f}")
    print(f"奖励方差: {std_reward**2:.6f}")
    print(f"收益率方差: {std_revenue_rate**2:.6f}")
    
    print("✓ 性能指标计算测试通过")


def test_constellation_modes():
    """测试不同星座模式"""
    print("\n" + "=" * 50)
    print("测试4: 星座模式")
    print("=" * 50)
    
    modes = ['cooperative', 'competitive', 'hybrid']
    
    for mode in modes:
        print(f"\n测试 {mode} 模式:")
        
        # 创建数据集
        dataset = ConstellationSMPDataset(
            size=10,
            num_samples=3,
            num_satellites=3
        )
        
        # 获取样本
        static, dynamic, _ = dataset[0]
        static_batch = static.unsqueeze(0)
        dynamic_batch = dynamic.unsqueeze(0)
        
        # 模拟任务执行
        tour_indices = torch.tensor([[0, 3, 6, 9]], dtype=torch.long)
        satellite_indices = torch.tensor([[0, 1, 2, 0]], dtype=torch.long)
        
        # 计算奖励
        R, _, _, _, _ = reward(static_batch, tour_indices, satellite_indices, mode)
        print(f"  奖励值: {R.item():.4f}")
        
        # 检查动态特征的变化
        print(f"  初始能量状态: {dynamic[4, :3, 0].numpy()}")
        print(f"  初始内存状态: {dynamic[6, :3, 0].numpy()}")
    
    print("✓ 星座模式测试通过")


def test_model_factory():
    """测试模型工厂"""
    print("\n" + "=" * 50)
    print("测试5: 模型工厂")
    print("=" * 50)
    
    # 测试支持的模型列表
    supported_models = ModelFactory.list_supported_models()
    print(f"支持的模型: {supported_models}")
    
    # 测试模型信息
    for model_type in supported_models:
        info = ModelFactory.get_model_info(model_type)
        print(f"{model_type}: {info['description']}")
    
    # 测试配置验证
    config_manager = ConfigManager()
    is_valid, errors = ModelFactory.validate_model_config('gpn', config_manager)
    print(f"GPN配置验证: {'通过' if is_valid else '失败'}")
    if errors:
        print(f"  错误: {errors}")
    
    print("✓ 模型工厂测试通过")


def main():
    """主测试函数"""
    print("开始快速测试...")
    print(f"使用设备: {device}")
    
    try:
        test_physical_constraints()
        test_performance_metrics()
        test_constellation_modes()
        test_model_factory()
        test_transformer_model()  # 最后测试，因为可能比较耗时
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("=" * 50)
        print("✓ 物理约束模型正常工作")
        print("✓ 性能指标计算正确")
        print("✓ 星座模式功能正常")
        print("✓ 模型工厂运行正常")
        print("✓ Transformer模型可以正常创建和运行")
        print("\n改进后的星座任务规划系统已准备就绪！")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
